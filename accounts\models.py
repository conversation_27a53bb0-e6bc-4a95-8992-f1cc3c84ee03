from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    """Custom User model with role-based authentication"""

    ROLE_CHOICES = [
        ('admin', 'Admin'),
        ('receptionist', 'Receptionist'),
        ('doctor', 'Doctor'),
        ('pharmacy', 'Pharmacy'),
        ('store_manager', 'Store Manager'),
    ]

    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='receptionist')
    phone = models.CharField(max_length=15, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    specialization = models.CharField(max_length=100, blank=True, null=True, help_text="Doctor's specialization")

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"

    @property
    def is_admin(self):
        return self.role == 'admin'

    @property
    def is_receptionist(self):
        return self.role == 'receptionist'

    @property
    def is_doctor(self):
        return self.role == 'doctor'

    @property
    def is_pharmacy(self):
        return self.role == 'pharmacy'

    @property
    def is_store_manager(self):
        return self.role == 'store_manager'
