"""
Management command to test the token system
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from patients.models import Patient
from tokens.models import DailyTokenCounter, PatientToken
from tokens.services import TokenService

User = get_user_model()

class Command(BaseCommand):
    help = 'Test the token generation and printing system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create test patient and user data',
        )
        parser.add_argument(
            '--test-token-generation',
            action='store_true',
            help='Test token generation',
        )
        parser.add_argument(
            '--test-printing',
            action='store_true',
            help='Test token printing (will output to file)',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting Token System Test...'))
        
        if options['create_test_data']:
            self.create_test_data()
        
        if options['test_token_generation']:
            self.test_token_generation()
        
        if options['test_printing']:
            self.test_printing()
        
        self.stdout.write(self.style.SUCCESS('Token System Test Completed!'))

    def create_test_data(self):
        """Create test user and patient data"""
        self.stdout.write('Creating test data...')
        
        # Create receptionist if doesn't exist
        receptionist, created = User.objects.get_or_create(
            username='test_receptionist',
            defaults={
                'password': 'testpass123',
                'role': 'receptionist',
                'first_name': 'Test',
                'last_name': 'Receptionist'
            }
        )
        if created:
            receptionist.set_password('testpass123')
            receptionist.save()
            self.stdout.write(f'Created receptionist: {receptionist.username}')
        else:
            self.stdout.write(f'Receptionist already exists: {receptionist.username}')
        
        # Create test patient if doesn't exist
        patient, created = Patient.objects.get_or_create(
            name='Test Patient',
            defaults={
                'age': 30,
                'gender': 'M',
                'contact': '+92-***********',
                'address': 'Test Address',
                'registered_by': receptionist
            }
        )
        if created:
            self.stdout.write(f'Created patient: {patient.name} (ID: {patient.patient_id})')
        else:
            self.stdout.write(f'Patient already exists: {patient.name} (ID: {patient.patient_id})')

    def test_token_generation(self):
        """Test token generation functionality"""
        self.stdout.write('Testing token generation...')
        
        try:
            # Get current token count
            current_count = DailyTokenCounter.get_current_token_number()
            self.stdout.write(f'Current token count for today: {current_count}')
            
            # Generate next token number
            next_token = DailyTokenCounter.get_next_token_number()
            self.stdout.write(f'Next token number: {next_token}')
            
            # Verify increment
            new_count = DailyTokenCounter.get_current_token_number()
            self.stdout.write(f'New token count: {new_count}')
            
            if new_count == current_count + 1:
                self.stdout.write(self.style.SUCCESS('✓ Token counter working correctly'))
            else:
                self.stdout.write(self.style.ERROR('✗ Token counter not working correctly'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error testing token generation: {str(e)}'))

    def test_printing(self):
        """Test token printing functionality"""
        self.stdout.write('Testing token printing...')
        
        try:
            # Get test data
            receptionist = User.objects.get(username='test_receptionist')
            patient = Patient.objects.get(name='Test Patient')
            
            # Initialize token service
            token_service = TokenService()
            
            # Check if patient already has a token for today
            from django.utils import timezone
            today = timezone.now().date()
            
            try:
                existing_token = PatientToken.objects.get(patient=patient, token_date=today)
                self.stdout.write(f'Patient already has token for today: #{existing_token.formatted_token_number}')
                token = existing_token
            except PatientToken.DoesNotExist:
                # Generate new token
                token = token_service.generate_token_for_patient(patient, receptionist)
                self.stdout.write(f'Generated new token: #{token.formatted_token_number}')
            
            # Test printing
            self.stdout.write('Testing token printing...')
            print_success = token_service.print_patient_token(token)
            
            if print_success:
                self.stdout.write(self.style.SUCCESS('✓ Token printing successful'))
                self.stdout.write(f'Token #{token.formatted_token_number} printed {token.print_count} time(s)')
                
                # Check if file output exists (for file printer type)
                import os
                from django.conf import settings
                printer_config = getattr(settings, 'THERMAL_PRINTER', {})
                if printer_config.get('TYPE') == 'file':
                    file_path = printer_config.get('FILE', {}).get('PATH', 'token_output.txt')
                    if os.path.exists(file_path):
                        self.stdout.write(f'Token output saved to: {file_path}')
                        with open(file_path, 'r') as f:
                            content = f.read()
                            self.stdout.write('Token content preview:')
                            self.stdout.write('-' * 40)
                            self.stdout.write(content[:200] + '...' if len(content) > 200 else content)
                            self.stdout.write('-' * 40)
            else:
                self.stdout.write(self.style.ERROR('✗ Token printing failed'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error testing token printing: {str(e)}'))

    def display_token_stats(self):
        """Display current token statistics"""
        self.stdout.write('\nToken Statistics:')
        self.stdout.write('-' * 30)
        
        # Today's token count
        today_count = DailyTokenCounter.get_current_token_number()
        self.stdout.write(f'Tokens generated today: {today_count}')
        
        # Total tokens in database
        total_tokens = PatientToken.objects.count()
        self.stdout.write(f'Total tokens in database: {total_tokens}')
        
        # Recent tokens
        recent_tokens = PatientToken.objects.order_by('-generated_at')[:5]
        if recent_tokens:
            self.stdout.write('\nRecent tokens:')
            for token in recent_tokens:
                self.stdout.write(f'  #{token.formatted_token_number} - {token.patient.name} ({token.generated_at.strftime("%Y-%m-%d %H:%M")})')
        else:
            self.stdout.write('No tokens found in database')
