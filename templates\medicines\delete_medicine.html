{% extends 'base.html' %}

{% block title %}Delete Medicine - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-danger">Delete Medicine</h1>
        <a href="{% url 'store:inventory' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Inventory
        </a>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Confirm Medicine Deletion</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-warning"></i> Warning!</h6>
                        <p class="mb-0">You are about to permanently delete this medicine from the store. This action cannot be undone.</p>
                    </div>
                    
                    <!-- Medicine Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Medicine Details:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Name:</th>
                                    <td><strong>{{ medicine.name }}</strong></td>
                                </tr>
                                {% if medicine.generic_name %}
                                <tr>
                                    <th>Generic Name:</th>
                                    <td>{{ medicine.generic_name }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>Category:</th>
                                    <td>{{ medicine.get_category_display }}</td>
                                </tr>
                                <tr>
                                    <th>Strength:</th>
                                    <td>{{ medicine.strength }}</td>
                                </tr>
                                {% if medicine.manufacturer %}
                                <tr>
                                    <th>Manufacturer:</th>
                                    <td>{{ medicine.manufacturer }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Stock Information:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Current Stock:</th>
                                    <td><strong>{{ medicine.quantity_in_stock }}</strong> {{ medicine.get_unit_display }}</td>
                                </tr>
                                <tr>
                                    <th>Price per Unit:</th>
                                    <td>₨{{ medicine.price_per_unit }}</td>
                                </tr>
                                <tr>
                                    <th>Total Value:</th>
                                    <td><strong>₨{{ medicine.total_value|floatformat:2 }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Min Stock Level:</th>
                                    <td>{{ medicine.minimum_stock_level }}</td>
                                </tr>
                                {% if medicine.expiry_date %}
                                <tr>
                                    <th>Expiry Date:</th>
                                    <td>{{ medicine.expiry_date|date:"M d, Y" }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>Added By:</th>
                                    <td>{{ medicine.created_by.first_name }} {{ medicine.created_by.last_name }}</td>
                                </tr>
                                <tr>
                                    <th>Added On:</th>
                                    <td>{{ medicine.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Safety Checks -->
                    {% if not can_delete %}
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> Cannot Delete</h6>
                            <p class="mb-0">This medicine is used in {{ prescription_count }} prescription(s) and cannot be deleted. Please contact the administrator if you need to remove this medicine.</p>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'store:inventory' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Inventory
                            </a>
                            <a href="{% url 'store:dashboard' %}" class="btn btn-primary">
                                <i class="fas fa-store"></i> Back to Store Dashboard
                            </a>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Safe to Delete</h6>
                            <p class="mb-0">This medicine is not used in any prescriptions and can be safely deleted.</p>
                        </div>
                        
                        <!-- Stock Warning -->
                        {% if medicine.quantity_in_stock > 0 %}
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-boxes"></i> Stock Warning</h6>
                            <p class="mb-0">This medicine has <strong>{{ medicine.quantity_in_stock }} {{ medicine.get_unit_display }}</strong> in stock worth <strong>₨{{ medicine.total_value|floatformat:2 }}</strong>. Deleting will remove all stock records.</p>
                        </div>
                        {% endif %}
                        
                        <!-- Confirmation Form -->
                        <form method="post">
                            {% csrf_token %}
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'store:inventory' %}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to delete {{ medicine.name }}? This will also delete all stock transaction records. This action cannot be undone.')">
                                    <i class="fas fa-trash"></i> Yes, Delete Medicine
                                </button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
