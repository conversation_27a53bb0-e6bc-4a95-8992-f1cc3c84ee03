# Generated by Django 5.2.4 on 2025-08-01 12:15

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('medicines', '0001_initial'),
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Prescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prescription_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('diagnosis', models.TextField()),
                ('symptoms', models.TextField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes for pharmacist', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('dispensed', 'Dispensed'), ('partially_dispensed', 'Partially Dispensed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('dispensed_at', models.DateTimeField(blank=True, null=True)),
                ('dispensed_by', models.ForeignKey(blank=True, limit_choices_to={'role': 'pharmacy'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dispensed_prescriptions', to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(limit_choices_to={'role': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='prescriptions', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prescriptions', to='patients.patient')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PrescriptionMedicine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField()),
                ('dosage', models.CharField(help_text='e.g., 1 tablet twice daily', max_length=100)),
                ('duration', models.CharField(help_text='e.g., 7 days, 2 weeks', max_length=50)),
                ('instructions', models.TextField(blank=True, help_text='Special instructions', null=True)),
                ('quantity_dispensed', models.PositiveIntegerField(default=0)),
                ('medicine', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='medicines.medicine')),
                ('prescription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prescription_medicines', to='prescriptions.prescription')),
            ],
            options={
                'unique_together': {('prescription', 'medicine')},
            },
        ),
    ]
