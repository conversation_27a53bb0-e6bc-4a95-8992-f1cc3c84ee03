{% extends 'base.html' %}

{% block title %}{{ patient.name }} - Patient Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Patient Details</h1>
        <div>
            {% if user.is_doctor or user.is_admin %}
                <a href="{% url 'create_prescription' patient.patient_id %}" class="btn btn-primary me-1">
                    <i class="fas fa-prescription"></i> Create Prescription
                </a>
            {% endif %}
            {% if user.is_receptionist or user.is_admin %}
                <!-- Token Button -->
                {% if patient.token %}
                    <a href="{% url 'tokens:preview' patient.token.id %}" class="btn btn-success me-1">
                        <i class="fas fa-ticket-alt"></i> View Token #{{ patient.token.formatted_token_number }}
                    </a>
                {% else %}
                    <form method="post" action="{% url 'tokens:print_patient_token' patient.patient_id %}" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-info me-1" onclick="return confirm('Generate and print token for {{ patient.name }}?')">
                            <i class="fas fa-print"></i> Generate Token
                        </button>
                    </form>
                {% endif %}
                <a href="{% url 'delete_patient' patient.patient_id %}" class="btn btn-danger me-1">
                    <i class="fas fa-trash"></i> Delete Patient
                </a>
            {% endif %}
            <a href="{% url 'patient_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Patient Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user"></i> Patient Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%">Patient ID:</th>
                            <td><strong>{{ patient.patient_id }}</strong></td>
                        </tr>
                        <tr>
                            <th>Name:</th>
                            <td>{{ patient.name }}</td>
                        </tr>
                        <tr>
                            <th>Age:</th>
                            <td>{{ patient.age }} years ({{ patient.age_group }})</td>
                        </tr>
                        <tr>
                            <th>Gender:</th>
                            <td>{{ patient.get_gender_display }}</td>
                        </tr>
                        <tr>
                            <th>Contact:</th>
                            <td>{{ patient.contact }}</td>
                        </tr>
                        {% if patient.address %}
                        <tr>
                            <th>Address:</th>
                            <td>{{ patient.address }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>Status:</th>
                            <td>
                                {% if patient.status == 'registered' %}
                                    <span class="badge bg-warning">{{ patient.get_status_display }}</span>
                                {% elif patient.status == 'consulting' %}
                                    <span class="badge bg-info">{{ patient.get_status_display }}</span>
                                {% elif patient.status == 'prescribed' %}
                                    <span class="badge bg-primary">{{ patient.get_status_display }}</span>
                                {% elif patient.status == 'completed' %}
                                    <span class="badge bg-success">{{ patient.get_status_display }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Assigned Doctor:</th>
                            <td>
                                {% if patient.assigned_doctor %}
                                    {{ patient.assigned_doctor.first_name }} {{ patient.assigned_doctor.last_name }}
                                {% else %}
                                    <span class="text-muted">Not assigned</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Registered By:</th>
                            <td>{{ patient.registered_by.first_name }} {{ patient.registered_by.last_name }}</td>
                        </tr>
                        <tr>
                            <th>Registration Date:</th>
                            <td>{{ patient.created_at|date:"M d, Y H:i" }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Token Information -->
            {% if patient.token %}
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-ticket-alt"></i> Token Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%">Token Number:</th>
                            <td><strong>#{{ patient.token.formatted_token_number }}</strong></td>
                        </tr>
                        <tr>
                            <th>Generated At:</th>
                            <td>{{ patient.token.generated_at|date:"M d, Y H:i" }}</td>
                        </tr>
                        <tr>
                            <th>Generated By:</th>
                            <td>{{ patient.token.generated_by.get_full_name }}</td>
                        </tr>
                        <tr>
                            <th>Print Count:</th>
                            <td>{{ patient.token.print_count }} time{{ patient.token.print_count|pluralize }}</td>
                        </tr>
                        {% if patient.token.printed_at %}
                        <tr>
                            <th>Last Printed:</th>
                            <td>{{ patient.token.printed_at|date:"M d, Y H:i" }}</td>
                        </tr>
                        {% endif %}
                    </table>
                    {% if user.is_receptionist or user.is_admin %}
                    <div class="mt-3">
                        <a href="{% url 'tokens:preview' patient.token.id %}" class="btn btn-sm btn-primary me-2">
                            <i class="fas fa-eye"></i> View Token
                        </a>
                        <form method="post" action="{% url 'tokens:reprint' patient.token.id %}" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Reprint token #{{ patient.token.formatted_token_number }}?')">
                                <i class="fas fa-print"></i> Reprint Token
                            </button>
                        </form>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Medical History -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-stethoscope"></i> Medical Information</h5>
                </div>
                <div class="card-body">
                    
                    {% if patient.prescriptions.exists %}
                    <h6>Recent Prescriptions:</h6>
                    <div class="list-group">
                        {% for prescription in patient.prescriptions.all|slice:":3" %}
                        <a href="{% url 'prescription_detail' prescription.prescription_id %}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ prescription.prescription_id }}</h6>
                                <small>{{ prescription.created_at|date:"M d, Y" }}</small>
                            </div>
                            <p class="mb-1">{{ prescription.diagnosis|truncatewords:10 }}</p>
                            <small>
                                Dr. {{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }} - 
                                <span class="badge bg-{{ prescription.status|yesno:'success,warning,danger' }}">
                                    {{ prescription.get_status_display }}
                                </span>
                            </small>
                        </a>
                        {% endfor %}
                    </div>
                    {% if patient.prescriptions.count > 3 %}
                    <div class="text-center mt-2">
                        <a href="{% url 'prescription_list' %}?search={{ patient.patient_id }}" class="btn btn-sm btn-outline-primary">
                            View All Prescriptions
                        </a>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-prescription fa-2x mb-2"></i>
                        <p>No prescriptions yet</p>
                        {% if user.is_doctor or user.is_admin %}
                        <a href="{% url 'create_prescription' patient.patient_id %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Create First Prescription
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
