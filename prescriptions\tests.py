from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from decimal import Decimal
from patients.models import Patient
from medicines.models import Medicine
from .models import Prescription, PrescriptionMedicine

User = get_user_model()

class PrescriptionModelTest(TestCase):
    """Test cases for the Prescription model"""

    def setUp(self):
        self.receptionist = User.objects.create_user(
            username='receptionist',
            password='testpass123',
            role='receptionist'
        )
        self.doctor = User.objects.create_user(
            username='doctor',
            password='testpass123',
            role='doctor'
        )
        self.pharmacy_user = User.objects.create_user(
            username='pharmacy',
            password='testpass123',
            role='pharmacy'
        )

        self.patient = Patient.objects.create(
            name='Test Patient',
            age=30,
            gender='M',
            contact='123456789',
            symptoms='Test symptoms',
            registered_by=self.receptionist,
            assigned_doctor=self.doctor
        )

        self.medicine = Medicine.objects.create(
            name='Test Medicine',
            category='tablet',
            strength='500mg',
            quantity_in_stock=100,
            unit='piece',
            price_per_unit=Decimal('5.00'),
            created_by=self.pharmacy_user
        )

    def test_create_prescription(self):
        """Test creating a prescription"""
        prescription = Prescription.objects.create(
            patient=self.patient,
            doctor=self.doctor,
            diagnosis='Test diagnosis',
            symptoms='Test symptoms',
            notes='Test notes'
        )

        self.assertEqual(prescription.patient, self.patient)
        self.assertEqual(prescription.doctor, self.doctor)
        self.assertEqual(prescription.diagnosis, 'Test diagnosis')
        self.assertEqual(prescription.status, 'pending')
        self.assertTrue(prescription.prescription_id.startswith('PR'))
        self.assertEqual(len(prescription.prescription_id), 6)  # PR + 4 digits

    def test_prescription_id_generation(self):
        """Test automatic prescription ID generation"""
        prescription1 = Prescription.objects.create(
            patient=self.patient,
            doctor=self.doctor,
            diagnosis='Diagnosis 1'
        )

        prescription2 = Prescription.objects.create(
            patient=self.patient,
            doctor=self.doctor,
            diagnosis='Diagnosis 2'
        )

        # Prescription IDs should be sequential
        id1_num = int(prescription1.prescription_id[2:])
        id2_num = int(prescription2.prescription_id[2:])
        self.assertEqual(id2_num, id1_num + 1)

    def test_prescription_total_cost(self):
        """Test prescription total cost calculation"""
        prescription = Prescription.objects.create(
            patient=self.patient,
            doctor=self.doctor,
            diagnosis='Test diagnosis'
        )

        # Add medicine to prescription
        PrescriptionMedicine.objects.create(
            prescription=prescription,
            medicine=self.medicine,
            quantity=10,
            dosage='1 tablet twice daily',
            duration='5 days'
        )

        expected_cost = 10 * self.medicine.price_per_unit
        self.assertEqual(prescription.total_cost, expected_cost)

    def test_prescription_can_be_dispensed(self):
        """Test prescription dispensability check"""
        prescription = Prescription.objects.create(
            patient=self.patient,
            doctor=self.doctor,
            diagnosis='Test diagnosis'
        )

        # Add medicine with available stock
        PrescriptionMedicine.objects.create(
            prescription=prescription,
            medicine=self.medicine,
            quantity=10,  # Less than available stock (100)
            dosage='1 tablet twice daily',
            duration='5 days'
        )

        self.assertTrue(prescription.can_be_dispensed)

        # Add medicine with insufficient stock
        low_stock_medicine = Medicine.objects.create(
            name='Low Stock Medicine',
            category='tablet',
            strength='250mg',
            quantity_in_stock=5,
            unit='piece',
            price_per_unit=Decimal('3.00'),
            created_by=self.pharmacy_user
        )

        PrescriptionMedicine.objects.create(
            prescription=prescription,
            medicine=low_stock_medicine,
            quantity=10,  # More than available stock (5)
            dosage='1 tablet daily',
            duration='10 days'
        )

        self.assertFalse(prescription.can_be_dispensed)


class PrescriptionMedicineModelTest(TestCase):
    """Test cases for the PrescriptionMedicine model"""

    def setUp(self):
        self.receptionist = User.objects.create_user(
            username='receptionist',
            password='testpass123',
            role='receptionist'
        )
        self.doctor = User.objects.create_user(
            username='doctor',
            password='testpass123',
            role='doctor'
        )
        self.pharmacy_user = User.objects.create_user(
            username='pharmacy',
            password='testpass123',
            role='pharmacy'
        )

        self.patient = Patient.objects.create(
            name='Test Patient',
            age=30,
            gender='M',
            contact='123456789',
            symptoms='Test symptoms',
            registered_by=self.receptionist,
            assigned_doctor=self.doctor
        )

        self.prescription = Prescription.objects.create(
            patient=self.patient,
            doctor=self.doctor,
            diagnosis='Test diagnosis'
        )

        self.medicine = Medicine.objects.create(
            name='Test Medicine',
            category='tablet',
            strength='500mg',
            quantity_in_stock=100,
            unit='piece',
            price_per_unit=Decimal('5.00'),
            created_by=self.pharmacy_user
        )

    def test_create_prescription_medicine(self):
        """Test creating a prescription medicine"""
        prescription_medicine = PrescriptionMedicine.objects.create(
            prescription=self.prescription,
            medicine=self.medicine,
            quantity=10,
            dosage='1 tablet twice daily',
            duration='5 days',
            instructions='Take after meals'
        )

        self.assertEqual(prescription_medicine.prescription, self.prescription)
        self.assertEqual(prescription_medicine.medicine, self.medicine)
        self.assertEqual(prescription_medicine.quantity, 10)
        self.assertEqual(prescription_medicine.quantity_dispensed, 0)

    def test_prescription_medicine_properties(self):
        """Test prescription medicine properties"""
        prescription_medicine = PrescriptionMedicine.objects.create(
            prescription=self.prescription,
            medicine=self.medicine,
            quantity=10,
            dosage='1 tablet twice daily',
            duration='5 days'
        )

        # Test total cost
        expected_cost = 10 * self.medicine.price_per_unit
        self.assertEqual(prescription_medicine.total_cost, expected_cost)

        # Test remaining quantity
        self.assertEqual(prescription_medicine.remaining_quantity, 10)

        # Test availability
        self.assertTrue(prescription_medicine.is_available)

        # Test dispensing status
        self.assertFalse(prescription_medicine.is_fully_dispensed)

        # Partially dispense
        prescription_medicine.quantity_dispensed = 5
        prescription_medicine.save()

        self.assertEqual(prescription_medicine.remaining_quantity, 5)
        self.assertFalse(prescription_medicine.is_fully_dispensed)

        # Fully dispense
        prescription_medicine.quantity_dispensed = 10
        prescription_medicine.save()

        self.assertEqual(prescription_medicine.remaining_quantity, 0)
        self.assertTrue(prescription_medicine.is_fully_dispensed)
