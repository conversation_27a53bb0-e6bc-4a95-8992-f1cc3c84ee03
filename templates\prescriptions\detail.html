{% extends 'base.html' %}

{% block title %}Prescription {{ prescription.prescription_id }} - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Prescription Details</h1>
        <div>
            {% if user.is_pharmacy and prescription.status != 'dispensed' and prescription.status != 'cancelled' %}
                <a href="{% url 'dispense_prescription' prescription.prescription_id %}" class="btn btn-success">
                    <i class="fas fa-hand-holding-medical"></i> Dispense
                </a>
            {% endif %}
            <a href="{% url 'prescription_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Prescription Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-prescription"></i> Prescription Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%">Prescription ID:</th>
                            <td><strong>{{ prescription.prescription_id }}</strong></td>
                        </tr>
                        <tr>
                            <th>Patient:</th>
                            <td>
                                <a href="{% url 'patient_detail' prescription.patient.patient_id %}">
                                    {{ prescription.patient.name }}
                                </a>
                                <br>
                                <small class="text-muted">{{ prescription.patient.patient_id }}</small>
                            </td>
                        </tr>
                        <tr>
                            <th>Doctor:</th>
                            <td>Dr. {{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</td>
                        </tr>
                        <tr>
                            <th>Date Created:</th>
                            <td>{{ prescription.created_at|date:"M d, Y H:i" }}</td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                {% if prescription.status == 'pending' %}
                                    <span class="badge bg-warning">{{ prescription.get_status_display }}</span>
                                {% elif prescription.status == 'partially_dispensed' %}
                                    <span class="badge bg-info">{{ prescription.get_status_display }}</span>
                                {% elif prescription.status == 'dispensed' %}
                                    <span class="badge bg-success">{{ prescription.get_status_display }}</span>
                                {% elif prescription.status == 'cancelled' %}
                                    <span class="badge bg-danger">{{ prescription.get_status_display }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% if prescription.dispensed_at %}
                        <tr>
                            <th>Dispensed Date:</th>
                            <td>{{ prescription.dispensed_at|date:"M d, Y H:i" }}</td>
                        </tr>
                        <tr>
                            <th>Dispensed By:</th>
                            <td>{{ prescription.dispensed_by.first_name }} {{ prescription.dispensed_by.last_name }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>Total Cost:</th>
                            <td><strong>₨{{ prescription.total_cost|floatformat:2 }}</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Medical Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-stethoscope"></i> Medical Information</h5>
                </div>
                <div class="card-body">
                    <h6>Diagnosis:</h6>
                    <div class="bg-light p-3 rounded mb-3">
                        {{ prescription.diagnosis|linebreaks }}
                    </div>
                    
                    {% if prescription.symptoms %}
                    <h6>Symptoms:</h6>
                    <div class="bg-light p-3 rounded mb-3">
                        {{ prescription.symptoms|linebreaks }}
                    </div>
                    {% endif %}
                    
                    {% if prescription.notes %}
                    <h6>Notes for Pharmacist:</h6>
                    <div class="bg-warning bg-opacity-10 p-3 rounded">
                        {{ prescription.notes|linebreaks }}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Prescribed Medicines -->
    <div class="card mt-4">
        <div class="card-header">
            <h5><i class="fas fa-pills"></i> Prescribed Medicines</h5>
        </div>
        <div class="card-body">
            {% if prescription_medicines %}
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>Medicine</th>
                                <th>Quantity</th>
                                <th>Dosage</th>
                                <th>Duration</th>
                                <th>Instructions</th>
                                <th>Cost</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in prescription_medicines %}
                            <tr>
                                <td>
                                    <strong>{{ item.medicine.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ item.medicine.strength }}</small>
                                </td>
                                <td>{{ item.quantity }} {{ item.medicine.get_unit_display }}</td>
                                <td>{{ item.dosage }}</td>
                                <td>{{ item.duration }}</td>
                                <td>
                                    {% if item.instructions %}
                                        {{ item.instructions }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>₨{{ item.total_cost|floatformat:2 }}</td>
                                <td>
                                    {% if prescription.status == 'dispensed' or item.is_fully_dispensed %}
                                        <span class="badge bg-success">Dispensed</span>
                                        <br>
                                        <small class="text-muted">{{ item.quantity_dispensed }}/{{ item.quantity }}</small>
                                    {% elif item.quantity_dispensed > 0 %}
                                        <span class="badge bg-info">Partial</span>
                                        <br>
                                        <small class="text-muted">{{ item.quantity_dispensed }}/{{ item.quantity }}</small>
                                    {% else %}
                                        <span class="badge bg-warning">Pending</span>
                                        <br>
                                        <small class="text-muted">0/{{ item.quantity }}</small>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="5">Total</th>
                                <th>₨{{ prescription.total_cost|floatformat:2 }}</th>
                                <th>
                                    {% if prescription.status == 'dispensed' %}
                                        <span class="badge bg-success">Complete</span>
                                    {% elif prescription.status == 'partially_dispensed' %}
                                        <span class="badge bg-info">Partial</span>
                                    {% else %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% endif %}
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No medicines prescribed</h6>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
