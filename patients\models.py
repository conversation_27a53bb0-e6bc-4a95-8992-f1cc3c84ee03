from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class Patient(models.Model):
    """Patient model for storing patient information"""

    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    STATUS_CHOICES = [
        ('registered', 'Registered'),
        ('consulting', 'Consulting'),
        ('prescribed', 'Prescribed'),
        ('completed', 'Completed'),
    ]

    # Patient Information
    patient_id = models.CharField(max_length=20, unique=True, editable=False)
    name = models.Char<PERSON>ield(max_length=100)
    age = models.PositiveIntegerField()
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    contact = models.CharField(max_length=15)
    address = models.TextField(blank=True, null=True)
    symptoms = models.TextField(blank=True, null=True)

    # System Information
    status = models.Char<PERSON>ield(max_length=20, choices=STATUS_CHOICES, default='registered')
    registered_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='registered_patients')
    assigned_doctor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                      related_name='assigned_patients', limit_choices_to={'role': 'doctor'})

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.patient_id} - {self.name}"

    def save(self, *args, **kwargs):
        if not self.patient_id:
            # Generate patient ID: THxxxx (TH + 4 digit number)
            last_patient = Patient.objects.order_by('-id').first()
            if last_patient:
                last_id = int(last_patient.patient_id[2:])  # Remove 'TH' prefix
                new_id = last_id + 1
            else:
                new_id = 1
            self.patient_id = f"TH{new_id:04d}"
        super().save(*args, **kwargs)

    @property
    def age_group(self):
        if self.age < 18:
            return "Child"
        elif self.age < 60:
            return "Adult"
        else:
            return "Senior"
