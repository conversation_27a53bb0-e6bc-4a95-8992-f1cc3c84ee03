from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import Medicine, StockTransaction

User = get_user_model()

class MedicineModelTest(TestCase):
    """Test cases for the Medicine model"""

    def setUp(self):
        self.pharmacy_user = User.objects.create_user(
            username='pharmacy',
            password='testpass123',
            role='pharmacy'
        )

    def test_create_medicine(self):
        """Test creating a medicine"""
        medicine = Medicine.objects.create(
            name='Paracetamol',
            generic_name='Acetaminophen',
            category='tablet',
            manufacturer='Test Pharma',
            strength='500mg',
            quantity_in_stock=100,
            unit='piece',
            price_per_unit=Decimal('2.50'),
            minimum_stock_level=20,
            created_by=self.pharmacy_user
        )

        self.assertEqual(medicine.name, 'Paracetamol')
        self.assertEqual(medicine.quantity_in_stock, 100)
        self.assertEqual(medicine.price_per_unit, Decimal('2.50'))
        self.assertFalse(medicine.is_low_stock)
        self.assertEqual(medicine.stock_status, 'In Stock')

    def test_medicine_stock_properties(self):
        """Test medicine stock-related properties"""
        # Test low stock
        low_stock_medicine = Medicine.objects.create(
            name='Low Stock Medicine',
            category='tablet',
            strength='100mg',
            quantity_in_stock=5,
            unit='piece',
            price_per_unit=Decimal('1.00'),
            minimum_stock_level=10,
            created_by=self.pharmacy_user
        )

        self.assertTrue(low_stock_medicine.is_low_stock)
        self.assertEqual(low_stock_medicine.stock_status, 'Low Stock')

        # Test out of stock
        out_of_stock_medicine = Medicine.objects.create(
            name='Out of Stock Medicine',
            category='tablet',
            strength='200mg',
            quantity_in_stock=0,
            unit='piece',
            price_per_unit=Decimal('3.00'),
            minimum_stock_level=5,
            created_by=self.pharmacy_user
        )

        self.assertEqual(out_of_stock_medicine.stock_status, 'Out of Stock')

    def test_medicine_total_value(self):
        """Test medicine total value calculation"""
        medicine = Medicine.objects.create(
            name='Test Medicine',
            category='tablet',
            strength='250mg',
            quantity_in_stock=50,
            unit='piece',
            price_per_unit=Decimal('5.00'),
            created_by=self.pharmacy_user
        )

        expected_value = 50 * Decimal('5.00')
        self.assertEqual(medicine.total_value, expected_value)

    def test_reduce_stock(self):
        """Test reducing medicine stock"""
        medicine = Medicine.objects.create(
            name='Test Medicine',
            category='tablet',
            strength='100mg',
            quantity_in_stock=20,
            unit='piece',
            price_per_unit=Decimal('2.00'),
            created_by=self.pharmacy_user
        )

        # Test successful stock reduction
        result = medicine.reduce_stock(5)
        self.assertTrue(result)
        self.assertEqual(medicine.quantity_in_stock, 15)

        # Test insufficient stock
        result = medicine.reduce_stock(25)
        self.assertFalse(result)
        self.assertEqual(medicine.quantity_in_stock, 15)  # Should remain unchanged

    def test_add_stock(self):
        """Test adding medicine stock"""
        medicine = Medicine.objects.create(
            name='Test Medicine',
            category='tablet',
            strength='100mg',
            quantity_in_stock=10,
            unit='piece',
            price_per_unit=Decimal('2.00'),
            created_by=self.pharmacy_user
        )

        medicine.add_stock(15)
        self.assertEqual(medicine.quantity_in_stock, 25)


class StockTransactionModelTest(TestCase):
    """Test cases for the StockTransaction model"""

    def setUp(self):
        self.pharmacy_user = User.objects.create_user(
            username='pharmacy',
            password='testpass123',
            role='pharmacy'
        )
        self.medicine = Medicine.objects.create(
            name='Test Medicine',
            category='tablet',
            strength='100mg',
            quantity_in_stock=50,
            unit='piece',
            price_per_unit=Decimal('2.00'),
            created_by=self.pharmacy_user
        )

    def test_create_stock_transaction(self):
        """Test creating a stock transaction"""
        transaction = StockTransaction.objects.create(
            medicine=self.medicine,
            transaction_type='in',
            quantity=20,
            reason='Stock replenishment',
            created_by=self.pharmacy_user
        )

        self.assertEqual(transaction.medicine, self.medicine)
        self.assertEqual(transaction.transaction_type, 'in')
        self.assertEqual(transaction.quantity, 20)
        self.assertEqual(transaction.reason, 'Stock replenishment')

    def test_stock_transaction_string_representation(self):
        """Test stock transaction string representation"""
        transaction = StockTransaction.objects.create(
            medicine=self.medicine,
            transaction_type='out',
            quantity=-5,
            reason='Dispensed',
            created_by=self.pharmacy_user
        )

        expected = f"{self.medicine.name} - Stock Out (-5)"
        self.assertEqual(str(transaction), expected)


class MedicineViewsTest(TestCase):
    """Test cases for medicine views"""

    def setUp(self):
        self.client = Client()
        self.pharmacy_user = User.objects.create_user(
            username='pharmacy',
            password='testpass123',
            role='pharmacy'
        )
        self.doctor = User.objects.create_user(
            username='doctor',
            password='testpass123',
            role='doctor'
        )

    def test_medicine_list_requires_permission(self):
        """Test medicine list requires pharmacy or admin role"""
        # Test with doctor (should be denied)
        self.client.login(username='doctor', password='testpass123')
        response = self.client.get(reverse('medicine_list'))
        self.assertRedirects(response, reverse('dashboard'))

        # Test with pharmacy user (should be allowed)
        self.client.login(username='pharmacy', password='testpass123')
        response = self.client.get(reverse('medicine_list'))
        self.assertEqual(response.status_code, 200)

    def test_add_medicine_post(self):
        """Test adding medicine via POST request"""
        self.client.login(username='pharmacy', password='testpass123')

        response = self.client.post(reverse('add_medicine'), {
            'name': 'Test Medicine',
            'generic_name': 'Test Generic',
            'category': 'tablet',
            'manufacturer': 'Test Pharma',
            'strength': '500mg',
            'quantity_in_stock': 100,
            'unit': 'piece',
            'price_per_unit': '2.50',
            'minimum_stock_level': 20
        })

        self.assertRedirects(response, reverse('medicine_list'))

        # Check if medicine was created
        medicine = Medicine.objects.get(name='Test Medicine')
        self.assertEqual(medicine.strength, '500mg')
        self.assertEqual(medicine.quantity_in_stock, 100)
        self.assertEqual(medicine.created_by, self.pharmacy_user)
