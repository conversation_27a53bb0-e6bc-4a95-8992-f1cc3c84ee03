from django.contrib import admin
from .models import Prescription, PrescriptionMedicine

# Remove Prescription models from admin - they will be managed through main interface
# class PrescriptionMedicineInline(admin.TabularInline):
#     """Inline for prescription medicines - Moved to main interface"""
#     pass

# @admin.register(Prescription)
# class PrescriptionAdmin(admin.ModelAdmin):
#     """Prescription Admin Configuration - Moved to main interface"""
#     pass

# @admin.register(PrescriptionMedicine)
# class PrescriptionMedicineAdmin(admin.ModelAdmin):
#     """Prescription Medicine Admin Configuration - Moved to main interface"""
#     pass
