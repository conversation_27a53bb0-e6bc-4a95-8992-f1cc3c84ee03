{% extends 'base.html' %}

{% block title %}Store Inventory - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-boxes"></i> Store Inventory</h1>
        <div>
            <a href="{% url 'store:add_medicine' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Medicine
            </a>
            <a href="{% url 'store:dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Store
            </a>
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query }}" placeholder="Medicine name, generic name, or manufacturer">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-control" id="category" name="category">
                        <option value="">All Categories</option>
                        {% for value, label in category_choices %}
                            <option value="{{ value }}" {% if category_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="stock" class="form-label">Stock Status</label>
                    <select class="form-control" id="stock" name="stock">
                        <option value="">All Stock</option>
                        <option value="low" {% if stock_filter == 'low' %}selected{% endif %}>Low Stock</option>
                        <option value="out" {% if stock_filter == 'out' %}selected{% endif %}>Out of Stock</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{% url 'store:inventory' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Inventory Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list"></i> Medicine Inventory</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Medicine Name</th>
                                <th>Category</th>
                                <th>Strength</th>
                                <th>Current Stock</th>
                                <th>Min Level</th>
                                <th>Status</th>
                                <th>Price/Unit</th>
                                <th>Total Value</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ medicine.name }}</strong>
                                    {% if medicine.generic_name %}
                                        <br><small class="text-muted">{{ medicine.generic_name }}</small>
                                    {% endif %}
                                    {% if medicine.manufacturer %}
                                        <br><small class="text-muted">{{ medicine.manufacturer }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ medicine.get_category_display }}</td>
                                <td>{{ medicine.strength }}</td>
                                <td>
                                    <strong>{{ medicine.quantity_in_stock }}</strong> {{ medicine.get_unit_display }}
                                </td>
                                <td>{{ medicine.minimum_stock_level }}</td>
                                <td>
                                    {% if medicine.quantity_in_stock == 0 %}
                                        <span class="badge bg-danger">Out of Stock</span>
                                    {% elif medicine.is_low_stock %}
                                        <span class="badge bg-warning">Low Stock</span>
                                    {% else %}
                                        <span class="badge bg-success">In Stock</span>
                                    {% endif %}
                                </td>
                                <td>₨{{ medicine.price_per_unit }}</td>
                                <td>₨{{ medicine.total_value|floatformat:2 }}</td>
                                <td>
                                    <a href="{% url 'store:restock' medicine.id %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-plus"></i> Restock
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Inventory pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if stock_filter %}&stock={{ stock_filter }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if stock_filter %}&stock={{ stock_filter }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if stock_filter %}&stock={{ stock_filter }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if stock_filter %}&stock={{ stock_filter }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No medicines in store</h5>
                    <p class="text-muted">Start by adding medicines to your store inventory.</p>
                    <a href="{% url 'store:add_medicine' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Medicine
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
