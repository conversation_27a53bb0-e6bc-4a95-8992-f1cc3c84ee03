from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages

User = get_user_model()

class UserModelTest(TestCase):
    """Test cases for the custom User model"""

    def setUp(self):
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'doctor',
            'phone': '+92-300-1234567',
            'address': 'Test Address'
        }

    def test_create_user(self):
        """Test creating a user with custom fields"""
        user = User.objects.create_user(**self.user_data)

        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, 'doctor')
        self.assertEqual(user.phone, '+92-300-1234567')
        self.assertTrue(user.check_password('testpass123'))

    def test_user_role_properties(self):
        """Test user role property methods"""
        # Test doctor role
        doctor = User.objects.create_user(username='doctor', role='doctor')
        self.assertTrue(doctor.is_doctor)
        self.assertFalse(doctor.is_receptionist)
        self.assertFalse(doctor.is_pharmacy)
        self.assertFalse(doctor.is_admin)

        # Test receptionist role
        receptionist = User.objects.create_user(username='receptionist', role='receptionist')
        self.assertTrue(receptionist.is_receptionist)
        self.assertFalse(receptionist.is_doctor)

        # Test pharmacy role
        pharmacy = User.objects.create_user(username='pharmacy', role='pharmacy')
        self.assertTrue(pharmacy.is_pharmacy)
        self.assertFalse(pharmacy.is_doctor)

        # Test admin role
        admin = User.objects.create_user(username='admin', role='admin')
        self.assertTrue(admin.is_admin)
        self.assertFalse(admin.is_doctor)

    def test_user_string_representation(self):
        """Test user string representation"""
        user = User.objects.create_user(username='testuser', role='doctor')
        expected = 'testuser (Doctor)'
        self.assertEqual(str(user), expected)


class AuthenticationViewsTest(TestCase):
    """Test cases for authentication views"""

    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            role='doctor'
        )

    def test_login_view_get(self):
        """Test login view GET request"""
        response = self.client.get(reverse('login'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Tahir Hospital')
        self.assertContains(response, 'Management System Login')

    def test_login_view_post_valid(self):
        """Test login view POST with valid credentials"""
        response = self.client.post(reverse('login'), {
            'username': 'testuser',
            'password': 'testpass123'
        })
        self.assertRedirects(response, reverse('dashboard'))

    def test_login_view_post_invalid(self):
        """Test login view POST with invalid credentials"""
        response = self.client.post(reverse('login'), {
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Invalid username or password' in str(m) for m in messages))

    def test_logout_view(self):
        """Test logout view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('logout'))
        self.assertRedirects(response, reverse('login'))

    def test_dashboard_redirect_by_role(self):
        """Test dashboard redirects based on user role"""
        # Test doctor dashboard
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Doctor Dashboard')

        # Test pharmacy user redirect
        pharmacy_user = User.objects.create_user(
            username='pharmacy', password='testpass123', role='pharmacy'
        )
        self.client.login(username='pharmacy', password='testpass123')
        response = self.client.get(reverse('dashboard'))
        self.assertRedirects(response, reverse('pharmacy_dashboard'))

    def test_dashboard_requires_login(self):
        """Test dashboard requires authentication"""
        response = self.client.get(reverse('dashboard'))
        self.assertRedirects(response, f"{reverse('login')}?next={reverse('dashboard')}")
