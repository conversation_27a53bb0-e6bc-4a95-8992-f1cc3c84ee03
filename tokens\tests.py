from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from patients.models import Patient
from .models import DailyTokenCounter, PatientToken
from .services import TokenService

User = get_user_model()

class TokenModelTest(TestCase):
    """Test cases for Token models"""

    def setUp(self):
        self.receptionist = User.objects.create_user(
            username='receptionist',
            password='testpass123',
            role='receptionist'
        )
        self.patient = Patient.objects.create(
            name='Test Patient',
            age=30,
            gender='M',
            contact='+92-***********',
            registered_by=self.receptionist
        )

    def test_daily_token_counter_creation(self):
        """Test daily token counter creation and increment"""
        # Get first token number
        token_num_1 = DailyTokenCounter.get_next_token_number()
        self.assertEqual(token_num_1, 1)

        # Get second token number
        token_num_2 = DailyTokenCounter.get_next_token_number()
        self.assertEqual(token_num_2, 2)

        # Check current token number
        current = DailyTokenCounter.get_current_token_number()
        self.assertEqual(current, 2)

    def test_patient_token_creation(self):
        """Test patient token creation"""
        token = PatientToken.objects.create(
            patient=self.patient,
            token_number=1,
            generated_by=self.receptionist
        )

        self.assertEqual(token.patient, self.patient)
        self.assertEqual(token.token_number, 1)
        self.assertEqual(token.formatted_token_number, '001')
        self.assertEqual(token.print_count, 0)
