{% extends 'base.html' %}

{% block title %}Store Management - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-store"></i> Store Management Dashboard</h1>
        <div>
            <a href="{% url 'store:add_medicine' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Medicine to Store
            </a>
            <a href="{% url 'store:inventory' %}" class="btn btn-success">
                <i class="fas fa-boxes"></i> View Inventory
            </a>
        </div>
    </div>
    
    <!-- Store Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_medicines }}</h4>
                            <p class="mb-0">Total Medicines</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pills fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ low_stock_count }}</h4>
                            <p class="mb-0">Low Stock Items</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-danger">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ out_of_stock_count }}</h4>
                            <p class="mb-0">Out of Stock</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>₨{{ total_value|floatformat:0 }}</h4>
                            <p class="mb-0">Total Inventory Value</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Low Stock Alerts -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-exclamation-triangle"></i> Low Stock Alerts</h5>
                </div>
                <div class="card-body">
                    {% if low_stock_alerts %}
                        <div class="list-group list-group-flush">
                            {% for medicine in low_stock_alerts %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ medicine.name }}</strong> ({{ medicine.strength }})
                                    <br>
                                    <small class="text-muted">Current: {{ medicine.quantity_in_stock }} | Min: {{ medicine.minimum_stock_level }}</small>
                                </div>
                                <div>
                                    <a href="{% url 'store:restock' medicine.id %}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-plus"></i> Restock
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{% url 'store:inventory' %}?stock=low" class="btn btn-outline-warning">
                                <i class="fas fa-list"></i> View All Low Stock Items
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-success mb-0">All medicines are well stocked!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Stock Transactions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> Recent Stock Transactions</h5>
                </div>
                <div class="card-body">
                    {% if recent_transactions %}
                        <div class="list-group list-group-flush">
                            {% for transaction in recent_transactions %}
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ transaction.medicine.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ transaction.reason }}</small>
                                    </div>
                                    <div class="text-end">
                                        {% if transaction.transaction_type == 'in' %}
                                            <span class="badge bg-success">+{{ transaction.quantity }}</span>
                                        {% elif transaction.transaction_type == 'out' %}
                                            <span class="badge bg-danger">{{ transaction.quantity }}</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ transaction.quantity }}</span>
                                        {% endif %}
                                        <br>
                                        <small class="text-muted">{{ transaction.created_at|date:"M d, H:i" }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{% url 'store:transactions' %}" class="btn btn-outline-primary">
                                <i class="fas fa-list"></i> View All Transactions
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No recent transactions</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Store Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'store:add_medicine' %}" class="btn btn-primary btn-lg d-block mb-2">
                                <i class="fas fa-plus"></i> Add New Medicine
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'store:inventory' %}" class="btn btn-success btn-lg d-block mb-2">
                                <i class="fas fa-boxes"></i> Manage Inventory
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'store:transactions' %}" class="btn btn-info btn-lg d-block mb-2">
                                <i class="fas fa-history"></i> Transaction History
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'store:inventory' %}?stock=low" class="btn btn-warning btn-lg d-block mb-2">
                                <i class="fas fa-exclamation-triangle"></i> Low Stock Report
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
