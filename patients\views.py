from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib.auth import get_user_model
from .models import Patient

User = get_user_model()

@login_required
def register_patient(request):
    """Register a new patient (Receptionist only)"""
    if not (request.user.is_receptionist or request.user.is_admin):
        messages.error(request, 'You do not have permission to register patients.')
        return redirect('dashboard')

    if request.method == 'POST':
        name = request.POST.get('name')
        age = request.POST.get('age')
        gender = request.POST.get('gender')
        contact = request.POST.get('contact')
        address = request.POST.get('address', '')

        assigned_doctor_id = request.POST.get('assigned_doctor')

        try:
            assigned_doctor = None
            if assigned_doctor_id:
                assigned_doctor = User.objects.get(id=assigned_doctor_id, role='doctor')

            patient = Patient.objects.create(
                name=name,
                age=int(age),
                gender=gender,
                contact=contact,
                address=address,
                registered_by=request.user,
                assigned_doctor=assigned_doctor
            )

            messages.success(request, f'Patient {patient.name} registered successfully with ID: {patient.patient_id}')
            return redirect('patient_list')

        except Exception as e:
            messages.error(request, f'Error registering patient: {str(e)}')

    # Get available doctors
    doctors = User.objects.filter(role='doctor')

    context = {
        'doctors': doctors
    }
    return render(request, 'patients/register.html', context)

@login_required
def patient_list(request):
    """List all patients with search and filter functionality"""
    patients = Patient.objects.all().select_related('registered_by', 'assigned_doctor')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        patients = patients.filter(
            Q(patient_id__icontains=search_query) |
            Q(name__icontains=search_query) |
            Q(contact__icontains=search_query)
        )

    # Filter by status
    status_filter = request.GET.get('status', '')
    if status_filter:
        patients = patients.filter(status=status_filter)

    # Filter by doctor (for doctors, show only their patients or unassigned patients)
    if request.user.is_doctor:
        patients = patients.filter(
            Q(assigned_doctor=request.user) |
            Q(assigned_doctor__isnull=True)
        )

    # Pagination
    paginator = Paginator(patients, 10)  # Show 10 patients per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_choices': Patient.STATUS_CHOICES,
    }
    return render(request, 'patients/list.html', context)

@login_required
def patient_detail(request, patient_id):
    """View patient details"""
    patient = get_object_or_404(Patient, patient_id=patient_id)

    # Check permissions
    if request.user.is_doctor and patient.assigned_doctor != request.user:
        messages.error(request, 'You can only view your assigned patients.')
        return redirect('patient_list')

    context = {
        'patient': patient
    }
    return render(request, 'patients/detail.html', context)

@login_required
def search_patients(request):
    """Search patients by name, ID, or contact"""
    query = request.GET.get('q', '')
    patients = []

    if query:
        patients = Patient.objects.filter(
            Q(name__icontains=query) |
            Q(patient_id__icontains=query) |
            Q(contact__icontains=query)
        ).select_related('registered_by', 'assigned_doctor')

        # Filter by doctor if user is doctor
        if request.user.is_doctor:
            patients = patients.filter(
                Q(assigned_doctor=request.user) |
                Q(assigned_doctor__isnull=True)
            )

    context = {
        'patients': patients,
        'query': query,
    }
    return render(request, 'patients/search.html', context)

@login_required
def delete_patient(request, patient_id):
    """Delete a patient (Receptionist or Admin only)"""
    if not (request.user.is_receptionist or request.user.is_admin):
        messages.error(request, 'You do not have permission to delete patients.')
        return redirect('dashboard')

    patient = get_object_or_404(Patient, patient_id=patient_id)

    # Check if patient has prescriptions - if yes, don't allow deletion
    if patient.prescriptions.exists():
        messages.error(request, f'Cannot delete patient {patient.name} ({patient.patient_id}) because they have existing prescriptions. Please contact admin.')
        return redirect('patient_detail', patient.patient_id)

    if request.method == 'POST':
        patient_name = patient.name
        patient_id_str = patient.patient_id
        patient.delete()
        messages.success(request, f'Patient {patient_name} ({patient_id_str}) has been deleted successfully.')
        return redirect('patient_list')

    context = {
        'patient': patient,
    }
    return render(request, 'patients/delete_confirm.html', context)

@login_required
def bulk_delete_patients(request):
    """Bulk delete patients (Receptionist or Admin only)"""
    if not (request.user.is_receptionist or request.user.is_admin):
        messages.error(request, 'You do not have permission to delete patients.')
        return redirect('dashboard')

    if request.method == 'POST':
        patient_ids = request.POST.getlist('patient_ids')
        if not patient_ids:
            messages.warning(request, 'No patients selected for deletion.')
            return redirect('patient_list')

        deleted_count = 0
        protected_count = 0

        for patient_id in patient_ids:
            try:
                patient = Patient.objects.get(patient_id=patient_id)
                if not patient.prescriptions.exists():
                    patient.delete()
                    deleted_count += 1
                else:
                    protected_count += 1
            except Patient.DoesNotExist:
                continue

        if deleted_count > 0:
            messages.success(request, f'Successfully deleted {deleted_count} patient(s).')
        if protected_count > 0:
            messages.warning(request, f'{protected_count} patient(s) could not be deleted because they have prescriptions.')

        return redirect('patient_list')

    return redirect('patient_list')
