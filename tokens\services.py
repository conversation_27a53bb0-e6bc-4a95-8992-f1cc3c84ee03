"""
Token generation and thermal printing services
"""
import logging
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from django.template.loader import render_to_string
from .models import DailyTokenCounter, PatientToken

logger = logging.getLogger(__name__)

class ThermalPrinterService:
    """Service for handling thermal printer operations"""
    
    def __init__(self):
        self.printer_config = getattr(settings, 'THERMAL_PRINTER', {})
        self.enabled = self.printer_config.get('ENABLED', True)
        self.printer_type = self.printer_config.get('TYPE', 'file')
        self.printer = None
        
    def _get_printer(self):
        """Initialize and return printer instance based on configuration"""
        if not self.enabled:
            logger.info("Thermal printer is disabled in settings")
            return None
            
        try:
            from escpos import printer
            
            if self.printer_type == 'usb':
                usb_config = self.printer_config.get('USB', {})
                self.printer = printer.Usb(
                    idVendor=usb_config.get('VENDOR_ID', 0x04b8),
                    idProduct=usb_config.get('PRODUCT_ID', 0x0202),
                    interface=usb_config.get('INTERFACE', 0),
                    in_ep=usb_config.get('IN_EP', 0x82),
                    out_ep=usb_config.get('OUT_EP', 0x01)
                )
            elif self.printer_type == 'serial':
                serial_config = self.printer_config.get('SERIAL', {})
                self.printer = printer.Serial(
                    devfile=serial_config.get('PORT', 'COM1'),
                    baudrate=serial_config.get('BAUDRATE', 9600),
                    bytesize=serial_config.get('BYTESIZE', 8),
                    parity=serial_config.get('PARITY', 'N'),
                    stopbits=serial_config.get('STOPBITS', 1),
                    timeout=serial_config.get('TIMEOUT', 1.0)
                )
            elif self.printer_type == 'network':
                network_config = self.printer_config.get('NETWORK', {})
                self.printer = printer.Network(
                    host=network_config.get('HOST', '*************'),
                    port=network_config.get('PORT', 9100)
                )
            elif self.printer_type == 'file':
                file_config = self.printer_config.get('FILE', {})
                self.printer = printer.File(
                    devfile=str(file_config.get('PATH', 'token_output.txt'))
                )
            else:
                logger.error(f"Unknown printer type: {self.printer_type}")
                return None
                
            return self.printer
            
        except ImportError:
            logger.error("python-escpos library not installed")
            return None
        except Exception as e:
            logger.error(f"Failed to initialize thermal printer: {str(e)}")
            return None
    
    def print_token(self, token_data):
        """Print patient token using thermal printer"""
        if not self.enabled:
            logger.info("Printing disabled - would print token for patient: %s", token_data.get('patient_name'))
            return True
            
        printer = self._get_printer()
        if not printer:
            logger.error("Failed to get printer instance")
            return False
            
        try:
            # Get printer settings
            settings_config = self.printer_config.get('SETTINGS', {})
            char_width = settings_config.get('CHAR_WIDTH', 32)

            # Print hospital name header
            printer.set(align='center', font='b', width=2, height=2)
            printer.text(f"{token_data['hospital_name']}\n")
            printer.text("-" * char_width + "\n")

            # Print token number (large and bold)
            printer.set(align='center', font='b', width=4, height=4)
            printer.text(f"TOKEN #{token_data['token_number']}\n")

            printer.set(align='center', font='a')
            printer.text("-" * char_width + "\n")

            # Print patient information
            printer.set(align='left', font='a')
            printer.text(f"Patient Name: {token_data['patient_name']}\n")
            printer.text(f"Patient ID: {token_data['patient_id']}\n")

            if token_data.get('doctor_name') and token_data['doctor_name'] != 'Not Assigned':
                printer.text(f"Doctor: {token_data['doctor_name']}\n")

            # Print date and time
            printer.text(f"Date & Time: {token_data['date_time']}\n")

            printer.text("-" * char_width + "\n")

            # Print simple footer
            printer.set(align='center', font='a')
            printer.text("Please wait for your turn\n")
            printer.text("Thank you!\n")
            
            # Cut paper if enabled
            if settings_config.get('CUT_PAPER', True):
                printer.cut()
            
            # Open drawer if enabled
            if settings_config.get('OPEN_DRAWER', False):
                printer.cashdraw(2)
                
            printer.close()
            logger.info("Token printed successfully for patient: %s", token_data['patient_name'])
            return True
            
        except Exception as e:
            logger.error(f"Failed to print token: {str(e)}")
            if printer:
                try:
                    printer.close()
                except:
                    pass
            return False


class TokenService:
    """Service for token generation and management"""
    
    def __init__(self):
        self.printer_service = ThermalPrinterService()
    
    def generate_token_for_patient(self, patient, generated_by):
        """Generate a new token for a patient"""
        try:
            # Get next token number for today
            token_number = DailyTokenCounter.get_next_token_number()
            
            # Create patient token record
            patient_token = PatientToken.objects.create(
                patient=patient,
                token_number=token_number,
                token_date=timezone.now().date(),
                generated_by=generated_by
            )
            
            logger.info(f"Generated token #{token_number} for patient {patient.name}")
            return patient_token
            
        except Exception as e:
            logger.error(f"Failed to generate token for patient {patient.name}: {str(e)}")
            raise
    
    def print_patient_token(self, patient_token):
        """Print a patient token using thermal printer"""
        try:
            # Get current time in Pakistan Standard Time
            from django.utils import timezone
            import pytz

            pst = pytz.timezone('Asia/Karachi')
            current_time = timezone.now().astimezone(pst)

            # Prepare token data with only required information
            token_data = {
                'hospital_name': getattr(settings, 'HOSPITAL_NAME', 'Tahir Hospital'),
                'token_number': patient_token.formatted_token_number,
                'patient_name': patient_token.patient.name,
                'patient_id': patient_token.patient.patient_id,
                'doctor_name': patient_token.patient.assigned_doctor.get_full_name() if patient_token.patient.assigned_doctor else 'Not Assigned',
                'date_time': current_time.strftime('%d/%m/%Y %I:%M %p'),
            }
            
            # Print the token
            success = self.printer_service.print_token(token_data)
            
            if success:
                # Mark token as printed
                patient_token.mark_as_printed()
                logger.info(f"Token #{patient_token.token_number} printed successfully")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to print token #{patient_token.token_number}: {str(e)}")
            return False
    
    def generate_and_print_token(self, patient, generated_by):
        """Generate and immediately print a token for a patient"""
        try:
            # Generate token
            patient_token = self.generate_token_for_patient(patient, generated_by)
            
            # Print token
            print_success = self.print_patient_token(patient_token)
            
            return patient_token, print_success
            
        except Exception as e:
            logger.error(f"Failed to generate and print token for patient {patient.name}: {str(e)}")
            raise
    
    def get_today_token_count(self):
        """Get total number of tokens generated today"""
        return DailyTokenCounter.get_current_token_number()
    
    def reprint_token(self, patient_token):
        """Reprint an existing token"""
        return self.print_patient_token(patient_token)
