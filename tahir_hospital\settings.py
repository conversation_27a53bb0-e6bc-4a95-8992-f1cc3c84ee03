"""
Django settings for tahir_hospital project.

Generated by 'django-admin startproject' using Django 5.2.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-gw_lx!3lzwqb1nj_m%_wxs%p6lslz5%b71nuo7dst^6c1fbl&7'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['************', 'localhost', '127.0.0.1']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Custom apps
    'accounts',
    'patients',
    'medicines',
    'prescriptions',
    'tokens',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'tahir_hospital.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'tahir_hospital.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Custom User Model
AUTH_USER_MODEL = 'accounts.User'

# Login URLs
LOGIN_URL = 'login'
LOGIN_REDIRECT_URL = 'dashboard'
LOGOUT_REDIRECT_URL = 'login'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Hospital Configuration
HOSPITAL_NAME = 'Tahir Hospital'
HOSPITAL_ADDRESS = 'Main Street, City, Country'
HOSPITAL_PHONE = '+92-XXX-XXXXXXX'

# Thermal Printer Configuration
THERMAL_PRINTER = {
    'ENABLED': True,  # Set to False to disable printing (for testing)
    'TYPE': 'usb',  # Options: 'usb', 'serial', 'network', 'file'

    # USB Printer Configuration (most common for thermal printers)
    'USB': {
        'VENDOR_ID': 0x04b8,  # Epson vendor ID (common for thermal printers)
        'PRODUCT_ID': 0x0202,  # Product ID (varies by model)
        'INTERFACE': 0,
        'IN_EP': 0x82,
        'OUT_EP': 0x01,
    },

    # Serial Printer Configuration
    'SERIAL': {
        'PORT': 'COM1',  # Windows: COM1, COM2, etc. Linux: /dev/ttyUSB0, etc.
        'BAUDRATE': 9600,
        'BYTESIZE': 8,
        'PARITY': 'N',
        'STOPBITS': 1,
        'TIMEOUT': 1.0,
    },

    # Network Printer Configuration
    'NETWORK': {
        'HOST': '*************',
        'PORT': 9100,
    },

    # File output (for testing without physical printer)
    'FILE': {
        'PATH': BASE_DIR / 'token_output.txt',
    },

    # Printer Settings
    'SETTINGS': {
        'CHAR_WIDTH': 32,  # Characters per line for thermal printer
        'PAPER_WIDTH': 58,  # Paper width in mm (58mm is common)
        'CUT_PAPER': True,  # Auto-cut paper after printing
        'OPEN_DRAWER': False,  # Open cash drawer (if connected)
    }
}
