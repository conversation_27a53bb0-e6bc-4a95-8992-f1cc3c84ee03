from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from decimal import Decimal

User = get_user_model()

class Medicine(models.Model):
    """Medicine model for inventory management"""

    CATEGORY_CHOICES = [
        ('tablet', 'Tablet'),
        ('capsule', 'Capsule'),
        ('syrup', 'Syrup'),
        ('injection', 'Injection'),
        ('ointment', 'Ointment'),
        ('drops', 'Drops'),
        ('inhaler', 'Inhaler'),
        ('other', 'Other'),
    ]

    UNIT_CHOICES = [
        ('piece', 'Piece'),
        ('bottle', 'Bottle'),
        ('vial', 'Vial'),
        ('tube', 'Tube'),
        ('pack', 'Pack'),
        ('box', 'Box'),
    ]

    # Medicine Information
    name = models.CharField(max_length=200)
    generic_name = models.CharField(max_length=200, blank=True, null=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    manufacturer = models.CharField(max_length=100, blank=True, null=True)
    strength = models.CharField(max_length=50, help_text="e.g., 500mg, 10ml")

    # Inventory Information
    quantity_in_stock = models.PositiveIntegerField(default=0)
    unit = models.CharField(max_length=20, choices=UNIT_CHOICES, default='piece')
    price_per_unit = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])

    # Stock Management
    minimum_stock_level = models.PositiveIntegerField(default=10, help_text="Alert when stock falls below this level")
    expiry_date = models.DateField(blank=True, null=True)

    # System Information
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_medicines')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['name']
        unique_together = ['name', 'strength']

    def __str__(self):
        return f"{self.name} ({self.strength})"

    @property
    def is_low_stock(self):
        """Check if medicine is running low on stock"""
        return self.quantity_in_stock <= self.minimum_stock_level

    @property
    def stock_status(self):
        """Get stock status as string"""
        if self.quantity_in_stock == 0:
            return "Out of Stock"
        elif self.is_low_stock:
            return "Low Stock"
        else:
            return "In Stock"

    @property
    def total_value(self):
        """Calculate total value of stock"""
        return self.quantity_in_stock * self.price_per_unit

    def reduce_stock(self, quantity):
        """Reduce stock quantity (used when dispensing)"""
        if self.quantity_in_stock >= quantity:
            self.quantity_in_stock -= quantity
            self.save()
            return True
        return False

    def add_stock(self, quantity):
        """Add stock quantity (used when restocking)"""
        self.quantity_in_stock += quantity
        self.save()


class StockTransaction(models.Model):
    """Track all stock movements"""

    TRANSACTION_TYPES = [
        ('in', 'Stock In'),
        ('out', 'Stock Out'),
        ('adjustment', 'Stock Adjustment'),
        ('expired', 'Expired Stock'),
    ]

    medicine = models.ForeignKey(Medicine, on_delete=models.CASCADE, related_name='stock_transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.IntegerField()  # Can be negative for stock out
    reason = models.CharField(max_length=200)
    reference = models.CharField(max_length=100, blank=True, null=True, help_text="Prescription ID, Purchase Order, etc.")

    # System Information
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.medicine.name} - {self.get_transaction_type_display()} ({self.quantity})"
