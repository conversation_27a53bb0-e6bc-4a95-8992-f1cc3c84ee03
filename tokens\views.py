from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.conf import settings
from patients.models import Patient
from .models import PatientToken
from .services import TokenService

@login_required
def token_preview(request, token_id):
    """Display token preview"""
    token = get_object_or_404(PatientToken, id=token_id)

    context = {
        'token': token,
        'hospital_name': getattr(settings, 'HOSPITAL_NAME', 'Tahir Hospital'),
        'hospital_address': getattr(settings, 'HOSPITAL_ADDRESS', 'Main Street, City'),
        'hospital_phone': getattr(settings, 'HOSPITAL_PHONE', '+92-XXX-XXXXXXX'),
    }
    return render(request, 'tokens/token_preview.html', context)

@login_required
def reprint_token(request, token_id):
    """Reprint an existing token"""
    if not (request.user.is_receptionist or request.user.is_admin):
        messages.error(request, 'You do not have permission to print tokens.')
        return redirect('dashboard')

    token = get_object_or_404(PatientToken, id=token_id)

    if request.method == 'POST':
        token_service = TokenService()
        success = token_service.reprint_token(token)

        if success:
            messages.success(request, f'Token #{token.formatted_token_number} has been reprinted successfully.')
        else:
            messages.error(request, 'Failed to print token. Please check printer connection.')

        return redirect('patient_detail', token.patient.patient_id)

    return redirect('tokens:preview', token_id)

@login_required
def print_patient_token(request, patient_id):
    """Print token for a patient (if they don't have one for today)"""
    if not (request.user.is_receptionist or request.user.is_admin):
        messages.error(request, 'You do not have permission to print tokens.')
        return redirect('dashboard')

    patient = get_object_or_404(Patient, patient_id=patient_id)

    # Check if patient already has a token for today
    from django.utils import timezone
    today = timezone.now().date()

    try:
        existing_token = PatientToken.objects.get(patient=patient, token_date=today)
        messages.info(request, f'Patient already has a token for today: #{existing_token.formatted_token_number}')
        return redirect('tokens:preview', existing_token.id)
    except PatientToken.DoesNotExist:
        pass

    if request.method == 'POST':
        token_service = TokenService()
        try:
            token, print_success = token_service.generate_and_print_token(patient, request.user)

            if print_success:
                messages.success(request, f'Token #{token.formatted_token_number} generated and printed successfully.')
            else:
                messages.warning(request, f'Token #{token.formatted_token_number} generated but printing failed. Please check printer connection.')

            return redirect('tokens:preview', token.id)

        except Exception as e:
            messages.error(request, f'Failed to generate token: {str(e)}')
            return redirect('patient_detail', patient.patient_id)

    return redirect('patient_detail', patient.patient_id)
