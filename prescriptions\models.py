from django.db import models
from django.contrib.auth import get_user_model
from patients.models import Patient
from medicines.models import Medicine

User = get_user_model()

class Prescription(models.Model):
    """Prescription model linking doctor, patient, and medicines"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('dispensed', 'Dispensed'),
        ('partially_dispensed', 'Partially Dispensed'),
        ('cancelled', 'Cancelled'),
    ]

    # Prescription Information
    prescription_id = models.CharField(max_length=20, unique=True, editable=False)
    patient = models.ForeignKey(Patient, on_delete=models.CASCADE, related_name='prescriptions')
    doctor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='prescriptions',
                              limit_choices_to={'role': 'doctor'})

    # Medical Information
    diagnosis = models.TextField()
    symptoms = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True, help_text="Additional notes for pharmacist")

    # Status and Timestamps
    status = models.Char<PERSON><PERSON>(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    dispensed_at = models.DateTimeField(null=True, blank=True)
    dispensed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='dispensed_prescriptions', limit_choices_to={'role': 'pharmacy'})

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.prescription_id} - {self.patient.name} by Dr. {self.doctor.first_name}"

    def save(self, *args, **kwargs):
        if not self.prescription_id:
            # Generate prescription ID: PRxxxx (PR + 4 digit number)
            last_prescription = Prescription.objects.order_by('-id').first()
            if last_prescription:
                last_id = int(last_prescription.prescription_id[2:])  # Remove 'PR' prefix
                new_id = last_id + 1
            else:
                new_id = 1
            self.prescription_id = f"PR{new_id:04d}"
        super().save(*args, **kwargs)

    @property
    def total_medicines(self):
        """Get total number of different medicines in prescription"""
        return self.prescription_medicines.count()

    @property
    def total_cost(self):
        """Calculate total cost of prescription"""
        total = 0
        for item in self.prescription_medicines.all():
            total += item.total_cost
        return total

    @property
    def can_be_dispensed(self):
        """Check if prescription can be dispensed (all medicines available)"""
        for item in self.prescription_medicines.all():
            if item.medicine.quantity_in_stock < item.quantity:
                return False
        return True


class PrescriptionMedicine(models.Model):
    """Individual medicine items in a prescription"""

    prescription = models.ForeignKey(Prescription, on_delete=models.CASCADE, related_name='prescription_medicines')
    medicine = models.ForeignKey(Medicine, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    dosage = models.CharField(max_length=100, help_text="e.g., 1 tablet twice daily")
    duration = models.CharField(max_length=50, help_text="e.g., 7 days, 2 weeks")
    instructions = models.TextField(blank=True, null=True, help_text="Special instructions")

    # Dispensing Information
    quantity_dispensed = models.PositiveIntegerField(default=0)

    class Meta:
        unique_together = ['prescription', 'medicine']

    def __str__(self):
        return f"{self.medicine.name} - {self.quantity} {self.medicine.get_unit_display()}"

    @property
    def total_cost(self):
        """Calculate total cost for this medicine item"""
        return self.quantity * self.medicine.price_per_unit

    @property
    def is_fully_dispensed(self):
        """Check if this medicine item is fully dispensed"""
        return self.quantity_dispensed >= self.quantity

    @property
    def remaining_quantity(self):
        """Get remaining quantity to be dispensed"""
        return self.quantity - self.quantity_dispensed

    @property
    def is_available(self):
        """Check if required quantity is available in stock"""
        return self.medicine.quantity_in_stock >= self.remaining_quantity
