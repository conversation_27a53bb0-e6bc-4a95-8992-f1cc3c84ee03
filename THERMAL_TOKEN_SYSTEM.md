# Thermal Token Printing System

## Overview

The Thermal Token Printing System has been successfully implemented for Tahir Hospital. This system automatically generates and prints patient tokens during registration, providing a professional queue management solution similar to what is used in hospitals worldwide.

## Features Implemented

### ✅ Core Features
- **Automatic Token Generation**: Tokens are automatically generated when patients are registered
- **Daily Reset**: Token numbers reset daily (001, 002, 003... each day)
- **Thermal Printer Support**: Full support for hospital-style thermal printers
- **Multiple Printer Types**: USB, Serial, Network, and File output support
- **Token Preview**: Web-based token preview and management
- **Reprint Functionality**: Ability to reprint tokens if needed
- **Print Tracking**: Tracks how many times each token has been printed

### ✅ Token Information Included
- **Hospital Name**: Tahir Hospital
- **Hospital Address**: Configurable in settings
- **Hospital Phone**: Configurable in settings
- **Token Number**: Daily sequential number (001, 002, etc.)
- **Date & Time**: When the token was generated
- **Patient Details**: Name, Age, Gender, Contact
- **Doctor Assignment**: Assigned doctor name (if any)

## System Components

### 1. Models (`tokens/models.py`)
- **DailyTokenCounter**: Manages daily token numbering with auto-reset
- **PatientToken**: Stores individual patient token information

### 2. Services (`tokens/services.py`)
- **ThermalPrinterService**: Handles thermal printer communication
- **TokenService**: Manages token generation and printing workflow

### 3. Views (`tokens/views.py`)
- **token_preview**: Display token preview
- **reprint_token**: Reprint existing tokens
- **print_patient_token**: Generate new tokens for patients

### 4. Templates
- **token_preview.html**: Web-based token preview with print styling

## Configuration

### Thermal Printer Settings (`settings.py`)

```python
# Hospital Configuration
HOSPITAL_NAME = 'Tahir Hospital'
HOSPITAL_ADDRESS = 'Main Street, City, Country'
HOSPITAL_PHONE = '+92-XXX-XXXXXXX'

# Thermal Printer Configuration
THERMAL_PRINTER = {
    'ENABLED': True,  # Set to False to disable printing
    'TYPE': 'usb',    # Options: 'usb', 'serial', 'network', 'file'
    
    # USB Printer (most common)
    'USB': {
        'VENDOR_ID': 0x04b8,  # Epson vendor ID
        'PRODUCT_ID': 0x0202, # Product ID (varies by model)
    },
    
    # Serial Printer
    'SERIAL': {
        'PORT': 'COM1',       # Windows: COM1, Linux: /dev/ttyUSB0
        'BAUDRATE': 9600,
    },
    
    # Network Printer
    'NETWORK': {
        'HOST': '*************',
        'PORT': 9100,
    },
    
    # File Output (for testing)
    'FILE': {
        'PATH': BASE_DIR / 'token_output.txt',
    },
    
    # Printer Settings
    'SETTINGS': {
        'CHAR_WIDTH': 32,     # Characters per line
        'PAPER_WIDTH': 58,    # Paper width in mm
        'CUT_PAPER': True,    # Auto-cut after printing
        'OPEN_DRAWER': False, # Open cash drawer
    }
}
```

## Usage Workflow

### 1. Patient Registration
1. Receptionist registers a new patient
2. System automatically generates next token number for the day
3. Token is immediately printed on thermal printer
4. Patient receives printed token with all details
5. Receptionist is redirected to token preview page

### 2. Token Management
- **View Token**: Click "View Token" button on patient detail page
- **Reprint Token**: Use "Reprint Token" button if needed
- **Generate Token**: For existing patients without tokens

### 3. Daily Reset
- Token numbers automatically reset to 001 each day
- Previous day's tokens remain in database for record keeping

## Printer Setup

### USB Thermal Printer
1. Connect USB thermal printer to computer
2. Install printer drivers if required
3. Find Vendor ID and Product ID using Device Manager (Windows) or lsusb (Linux)
4. Update `THERMAL_PRINTER['USB']` settings in Django settings
5. Set `THERMAL_PRINTER['TYPE'] = 'usb'`

### Serial Thermal Printer
1. Connect serial thermal printer to COM port
2. Identify correct COM port (COM1, COM2, etc.)
3. Update `THERMAL_PRINTER['SERIAL']` settings
4. Set `THERMAL_PRINTER['TYPE'] = 'serial'`

### Network Thermal Printer
1. Connect printer to network
2. Configure printer IP address
3. Update `THERMAL_PRINTER['NETWORK']` settings
4. Set `THERMAL_PRINTER['TYPE'] = 'network'`

### Testing Mode
1. Set `THERMAL_PRINTER['TYPE'] = 'file'`
2. Tokens will be saved to text file instead of printing
3. Useful for testing without physical printer

## Token Format

```
        TAHIR HOSPITAL
    Main Street, City, Country
      Phone: +92-XXX-XXXXXXX
    --------------------------------

           TOKEN #001

         05/08/2025 14:30
    --------------------------------

    PATIENT DETAILS:
    Name: John Doe
    Age: 30 years
    Gender: Male
    Contact: +92-***********
    Doctor: Dr. Smith

    --------------------------------
        Please wait for your turn
         Keep this token safe
        Thank you for visiting!
```

## Database Schema

### DailyTokenCounter
- `date`: Date for token counter
- `current_token_number`: Current token number for the day
- `created_at`, `updated_at`: Timestamps

### PatientToken
- `patient`: Foreign key to Patient
- `token_number`: Token number for the day
- `token_date`: Date when token was generated
- `generated_by`: User who generated the token
- `generated_at`: Timestamp when generated
- `printed_at`: Timestamp when last printed
- `print_count`: Number of times printed

## Security & Permissions

- **Token Generation**: Only Receptionists and Admins
- **Token Reprinting**: Only Receptionists and Admins
- **Token Viewing**: All authenticated users
- **Token Management**: Admin interface available

## Dependencies

- **python-escpos**: Thermal printer communication library
- **Pillow**: Image processing (included with escpos)
- **PyYAML**: Configuration support
- **pyserial**: Serial port communication
- **pyusb**: USB communication

## Installation Commands

```bash
# Install thermal printer dependencies
pip install python-escpos[usb,serial]

# Run migrations
python manage.py migrate

# Test token system (optional)
python manage.py test_token_system --create-test-data --test-token-generation --test-printing
```

## Troubleshooting

### Common Issues

1. **Printer Not Found**
   - Check USB/Serial connection
   - Verify Vendor/Product IDs
   - Install printer drivers

2. **Permission Denied**
   - Run as administrator (Windows)
   - Add user to dialout group (Linux)
   - Check USB permissions

3. **Token Not Printing**
   - Check printer power and paper
   - Verify printer settings
   - Test with file output first

4. **Daily Reset Not Working**
   - Check timezone settings
   - Verify database connectivity
   - Check DailyTokenCounter model

### Testing

```python
# Test token generation in Django shell
from tokens.services import TokenService
from tokens.models import DailyTokenCounter

# Check current token count
print(DailyTokenCounter.get_current_token_number())

# Generate next token
print(DailyTokenCounter.get_next_token_number())
```

## Future Enhancements

- **Queue Display**: Digital display showing current token being served
- **SMS Notifications**: Send token details via SMS
- **Mobile App**: Patient mobile app for token status
- **Analytics**: Token generation and wait time analytics
- **Multi-Department**: Separate token sequences for different departments

---

**Implementation Status**: ✅ Complete
**Last Updated**: August 5, 2025
**Version**: 1.0
