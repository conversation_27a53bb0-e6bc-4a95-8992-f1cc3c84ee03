{% extends 'base.html' %}

{% block title %}Dispense Prescription - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Dispense Prescription</h1>
        <a href="{% url 'prescription_detail' prescription.prescription_id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Details
        </a>
    </div>
    
    <!-- Prescription Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-prescription"></i> Prescription Details</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%">Prescription ID:</th>
                            <td><strong>{{ prescription.prescription_id }}</strong></td>
                        </tr>
                        <tr>
                            <th>Patient:</th>
                            <td>{{ prescription.patient.name }} ({{ prescription.patient.patient_id }})</td>
                        </tr>
                        <tr>
                            <th>Doctor:</th>
                            <td>Dr. {{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</td>
                        </tr>
                        <tr>
                            <th>Date:</th>
                            <td>{{ prescription.created_at|date:"M d, Y H:i" }}</td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                {% if prescription.status == 'pending' %}
                                    <span class="badge bg-warning">Pending</span>
                                {% elif prescription.status == 'partially_dispensed' %}
                                    <span class="badge bg-info">Partially Dispensed</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-stethoscope"></i> Medical Information</h5>
                </div>
                <div class="card-body">
                    <h6>Diagnosis:</h6>
                    <p>{{ prescription.diagnosis }}</p>
                    {% if prescription.notes %}
                    <h6>Notes:</h6>
                    <p class="text-muted">{{ prescription.notes }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Dispensing Form -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-hand-holding-medical"></i> Dispense Medicines</h5>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>Medicine</th>
                                <th>Prescribed</th>
                                <th>Already Dispensed</th>
                                <th>Remaining</th>
                                <th>Available Stock</th>
                                <th>Dispense Now</th>
                                <th>Dosage & Instructions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in prescription_medicines %}
                            <tr>
                                <td>
                                    <strong>{{ item.medicine.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ item.medicine.strength }}</small>
                                </td>
                                <td>{{ item.quantity }} {{ item.medicine.get_unit_display }}</td>
                                <td>{{ item.quantity_dispensed }} {{ item.medicine.get_unit_display }}</td>
                                <td>
                                    <strong>{{ item.remaining_quantity }} {{ item.medicine.get_unit_display }}</strong>
                                </td>
                                <td>
                                    {% if item.medicine.quantity_in_stock >= item.remaining_quantity %}
                                        <span class="text-success">{{ item.medicine.quantity_in_stock }} {{ item.medicine.get_unit_display }}</span>
                                    {% elif item.medicine.quantity_in_stock > 0 %}
                                        <span class="text-warning">{{ item.medicine.quantity_in_stock }} {{ item.medicine.get_unit_display }}</span>
                                    {% else %}
                                        <span class="text-danger">Out of Stock</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.remaining_quantity > 0 and item.medicine.quantity_in_stock > 0 %}
                                        <input type="number" 
                                               class="form-control" 
                                               name="quantity_{{ item.id }}" 
                                               min="0" 
                                               max="{{ item.remaining_quantity|add:0 }}"
                                               value="{{ item.remaining_quantity }}"
                                               style="width: 100px;">
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>Dosage:</strong> {{ item.dosage }}<br>
                                    <strong>Duration:</strong> {{ item.duration }}<br>
                                    {% if item.instructions %}
                                        <strong>Instructions:</strong> {{ item.instructions }}
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Dispensing Instructions:</h6>
                            <ul class="mb-0">
                                <li>Verify patient identity before dispensing</li>
                                <li>Check medicine expiry dates</li>
                                <li>Provide proper dosage instructions</li>
                                <li>Stock will be automatically deducted</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'prescription_detail' prescription.prescription_id %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-hand-holding-medical"></i> Dispense Medicines
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Auto-calculate maximum dispensable quantity based on stock
document.addEventListener('DOMContentLoaded', function() {
    const quantityInputs = document.querySelectorAll('input[type="number"]');
    
    quantityInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            const max = parseInt(this.getAttribute('max'));
            const value = parseInt(this.value);
            
            if (value > max) {
                this.value = max;
            }
        });
    });
});
</script>
{% endblock %}
