# Tahir Hospital Management System

A comprehensive web-based Hospital Management System built with Django that reflects the real workflow of a hospital. The system manages three main user roles: Receptionist, Doctor, and Pharmacy.

## 🏥 System Overview

The Tahir Hospital Management System streamlines hospital operations by providing role-based access and automated workflows for patient management, prescription handling, and inventory control.

### Key Features

- **Role-based Authentication**: Admin, Receptionist, Doctor, and Pharmacy roles
- **Patient Management**: Complete patient registration and tracking system
- **Prescription System**: Digital prescription creation and management
- **Inventory Management**: Medicine stock tracking with automatic deduction
- **Automated Workflows**: Seamless integration between all modules
- **Real-time Stock Alerts**: Low stock and out-of-stock notifications

## 🔹 System Workflow

### Receptionist
- Registers new patients with personal details and symptoms
- Patient data is automatically forwarded to assigned doctors
- Can view all patients and their status
- **Can delete patients** (only if they have no prescriptions)
- Bulk delete functionality for multiple patients

### Doctor
- Views patients registered by receptionists
- Adds diagnosis and prescribes medicines from predefined list
- Prescriptions are automatically saved and linked to patients
- Can track prescription history

### Pharmacy
- Views prescriptions created by doctors
- Dispenses prescribed medicines to patients
- **Automatic Stock Deduction**: When medicines are issued, quantities are automatically reduced from inventory
- **Separate from Store Management** - focuses only on dispensing

### Store Manager
- **Dedicated role for inventory management**
- Adds new medicines to store
- Manages stock levels and restocking
- Monitors low stock alerts
- **Automatic stock deduction** when pharmacy dispenses medicines

## 🔹 Core Modules

1. **Patient Registration** (Receptionist)
2. **Doctor Dashboard** (View patients & write prescriptions)
3. **Medicine & Stock Management** (Add/edit medicines with quantity tracking)
4. **Prescription Management** (Linking doctor, patient, and medicine)
5. **Pharmacy Interface** (View & dispense prescriptions)
6. **Stock Deduction Logic** (Automatic inventory updates)
7. **User Authentication** (Role-based access control)

## 🚀 Installation & Setup

### Prerequisites
- Python 3.8+
- Django 5.2+
- SQLite (default) or PostgreSQL/MySQL

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tahir-hospital
   ```

2. **Install dependencies**
   ```bash
   pip install django
   ```

3. **Run migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

4. **Create initial users**
   ```bash
   python manage.py create_initial_users
   ```

5. **Create sample medicines**
   ```bash
   python manage.py create_sample_medicines
   ```

6. **Create sample data (optional)**
   ```bash
   python manage.py create_sample_data
   ```

7. **Run the development server**
   ```bash
   python manage.py runserver
   ```

8. **Access the application**
   - Open your browser and go to `http://127.0.0.1:8000`
   - Use the login credentials provided below

## 👥 Default User Accounts

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| Admin | admin | admin123 | Full system access |
| Receptionist | receptionist | receptionist123 | Patient registration |
| Doctor | dr_kashif | doctor123 | Dr. Kashif |
| Doctor | dr_tahir | doctor123 | Dr. Tahir |
| Pharmacy | pharmacy | pharmacy123 | Medicine management |

## 📱 User Interfaces

### Admin Dashboard
- System overview and statistics
- User management
- Access to Django admin panel
- Complete system control

### Receptionist Dashboard
- Patient registration form
- Patient search and listing
- Today's registration statistics
- Quick access to patient records

### Doctor Dashboard
- Assigned patient list
- Prescription creation interface
- Patient consultation history
- Medical diagnosis tools

### Pharmacy Dashboard
- Pending prescriptions queue
- Medicine inventory management
- Stock alerts and notifications
- Dispensing interface with automatic stock deduction

## 🔧 Technical Features

### Database Models
- **User**: Custom user model with role-based permissions
- **Patient**: Complete patient information with auto-generated IDs
- **Medicine**: Inventory management with stock tracking
- **Prescription**: Links patients, doctors, and medicines
- **PrescriptionMedicine**: Individual medicine items in prescriptions
- **StockTransaction**: Complete audit trail of stock movements

### Security Features
- Role-based access control
- CSRF protection
- User authentication and session management
- Permission-based view access

### Automation Features
- **Auto-generated IDs**: Patient IDs (TH0001) and Prescription IDs (PR0001)
- **Automatic Stock Deduction**: Real-time inventory updates when medicines are dispensed
- **Stock Alerts**: Automatic notifications for low stock and out-of-stock items
- **Status Updates**: Automatic patient status updates through workflow

## 🧪 Testing

The system includes comprehensive test coverage:

```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test accounts
python manage.py test patients
python manage.py test medicines
python manage.py test prescriptions
```

### Test Coverage
- **32 test cases** covering all major functionality
- Model testing for data integrity
- View testing for proper access control
- Integration testing for workflow automation

## 📊 Sample Data

The system includes management commands to create sample data:

- **8 sample medicines** with various categories and stock levels
- **5 sample patients** with different medical conditions
- **3 sample prescriptions** demonstrating the complete workflow
- **Stock transactions** showing inventory movements

## 🔄 Workflow Example

1. **Receptionist** registers a new patient with symptoms
2. **Patient** is assigned to a doctor (Dr. Kashif or Dr. Tahir)
3. **Doctor** reviews patient and creates prescription with medicines
4. **Prescription** appears in pharmacy dashboard as pending
5. **Pharmacy** dispenses medicines to patient
6. **Stock** is automatically deducted from inventory
7. **Patient status** is updated to completed

## 🛠️ Technology Stack

- **Backend**: Django 5.2.4
- **Database**: SQLite (development) / PostgreSQL (production)
- **Frontend**: Bootstrap 5.1.3, HTML5, CSS3, JavaScript
- **Icons**: Font Awesome 6.0
- **Authentication**: Django's built-in authentication system

## 📁 Project Structure

```
tahir_hospital/
├── accounts/           # User management and authentication
├── patients/           # Patient registration and management
├── medicines/          # Medicine inventory and stock management
├── prescriptions/      # Prescription creation and dispensing
├── templates/          # HTML templates
├── static/            # CSS, JS, and static files
├── tahir_hospital/    # Main project settings
└── manage.py          # Django management script
```

## 🚀 Production Deployment

For production deployment:

1. Set `DEBUG = False` in settings.py
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving
4. Configure email settings for notifications
5. Set up proper logging
6. Use environment variables for sensitive settings

## 📝 License

This project is developed for Tahir Hospital and is intended for educational and operational use.

## 👨‍💻 Development

The system is built with scalability and maintainability in mind:
- Modular Django app structure
- Comprehensive test coverage
- Clean code practices
- Detailed documentation
- Role-based architecture

For any questions or support, please contact the development team.
