from django.contrib import admin
from .models import Medicine, StockTransaction

# Remove Medicine and StockTransaction from admin - they will be managed through Store interface
# @admin.register(Medicine)
# class MedicineAdmin(admin.ModelAdmin):
#     """Medicine Admin Configuration - Moved to Store interface"""
#     pass

# @admin.register(StockTransaction)
# class StockTransactionAdmin(admin.ModelAdmin):
#     """Stock Transaction Admin Configuration - Moved to Store interface"""
#     pass
