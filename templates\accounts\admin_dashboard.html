{% extends 'base.html' %}

{% block title %}Admin Dashboard - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4">Admin Dashboard</h1>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card text-white bg-primary mb-3">
                <div class="card-header">
                    <i class="fas fa-users"></i> Total Users
                </div>
                <div class="card-body">
                    <h4 class="card-title">{{ total_users|default:0 }}</h4>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success mb-3">
                <div class="card-header">
                    <i class="fas fa-user-injured"></i> Total Patients
                </div>
                <div class="card-body">
                    <h4 class="card-title">{{ total_patients|default:0 }}</h4>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning mb-3">
                <div class="card-header">
                    <i class="fas fa-pills"></i> Total Medicines
                </div>
                <div class="card-body">
                    <h4 class="card-title">{{ total_medicines|default:0 }}</h4>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info mb-3">
                <div class="card-header">
                    <i class="fas fa-prescription"></i> Total Prescriptions
                </div>
                <div class="card-body">
                    <h4 class="card-title">{{ total_prescriptions|default:0 }}</h4>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cog"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="/admin/" class="btn btn-primary mb-2 d-block">
                        <i class="fas fa-tools"></i> Django Admin Panel
                    </a>
                    <a href="/admin/accounts/user/add/" class="btn btn-success mb-2 d-block">
                        <i class="fas fa-user-plus"></i> Add New User
                    </a>
                    <a href="{% url 'store:dashboard' %}" class="btn btn-warning mb-2 d-block">
                        <i class="fas fa-store"></i> Store Management
                    </a>
                    <a href="{% url 'pharmacy_dashboard' %}" class="btn btn-info mb-2 d-block">
                        <i class="fas fa-clipboard-list"></i> Pharmacy Dashboard
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> System Overview</h5>
                </div>
                <div class="card-body">
                    <p><strong>Hospital:</strong> {{ hospital_name }}</p>
                    <p><strong>System Status:</strong> <span class="badge bg-success">Online</span></p>
                    <p><strong>Available Doctors:</strong></p>
                    <ul>
                        <li>Dr. Kashif - General Medicine</li>
                        <li>Dr. Tahir - Homeopathic Doctor and Nutritionist</li>
                    </ul>
                    <p><strong>Last Login:</strong> {{ user.last_login|date:"M d, Y H:i" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
