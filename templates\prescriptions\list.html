{% extends 'base.html' %}

{% block title %}Prescriptions - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Prescriptions</h1>
        {% if user.is_doctor or user.is_admin %}
        <a href="{% url 'patient_list' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create New Prescription
        </a>
        {% endif %}
    </div>
    
    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query }}" placeholder="Prescription ID, Patient name, or Doctor">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{% url 'prescription_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Prescriptions Table -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Prescription ID</th>
                                <th>Patient</th>
                                <th>Doctor</th>
                                <th>Diagnosis</th>
                                <th>Medicines</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prescription in page_obj %}
                            <tr>
                                <td><strong>{{ prescription.prescription_id }}</strong></td>
                                <td>
                                    {{ prescription.patient.name }}
                                    <br>
                                    <small class="text-muted">{{ prescription.patient.patient_id }}</small>
                                </td>
                                <td>Dr. {{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</td>
                                <td>{{ prescription.diagnosis|truncatewords:5 }}</td>
                                <td>
                                    <span class="badge bg-info">{{ prescription.total_medicines }} items</span>
                                    <br>
                                    <small class="text-muted">₨{{ prescription.total_cost|floatformat:2 }}</small>
                                </td>
                                <td>
                                    {% if prescription.status == 'pending' %}
                                        <span class="badge bg-warning">{{ prescription.get_status_display }}</span>
                                    {% elif prescription.status == 'partially_dispensed' %}
                                        <span class="badge bg-info">{{ prescription.get_status_display }}</span>
                                    {% elif prescription.status == 'dispensed' %}
                                        <span class="badge bg-success">{{ prescription.get_status_display }}</span>
                                    {% elif prescription.status == 'cancelled' %}
                                        <span class="badge bg-danger">{{ prescription.get_status_display }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ prescription.created_at|date:"M d, Y H:i" }}</td>
                                <td>
                                    <a href="{% url 'prescription_detail' prescription.prescription_id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    {% if user.is_pharmacy and prescription.status != 'dispensed' and prescription.status != 'cancelled' %}
                                    <a href="{% url 'dispense_prescription' prescription.prescription_id %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-hand-holding-medical"></i> Dispense
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Prescriptions pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-prescription fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No prescriptions found</h5>
                    {% if user.is_doctor or user.is_admin %}
                    <p class="text-muted">Start by creating prescriptions for your patients.</p>
                    <a href="{% url 'patient_list' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create First Prescription
                    </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
