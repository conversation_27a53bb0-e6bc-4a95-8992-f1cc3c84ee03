from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from medicines.models import Medicine, StockTransaction
from datetime import date, timedelta

User = get_user_model()

class Command(BaseCommand):
    help = 'Create sample medicines for Tahir Hospital'

    def handle(self, *args, **options):
        # Get pharmacy user to assign as creator
        try:
            pharmacy_user = User.objects.get(role='pharmacy')
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('Pharmacy user not found. Please create users first.'))
            return

        sample_medicines = [
            {
                'name': 'Paracetamol',
                'generic_name': 'Acetaminophen',
                'category': 'tablet',
                'manufacturer': 'ABC Pharma',
                'strength': '500mg',
                'quantity_in_stock': 100,
                'unit': 'piece',
                'price_per_unit': 2.50,
                'minimum_stock_level': 20,
            },
            {
                'name': 'Amoxicillin',
                'generic_name': 'Amoxicillin',
                'category': 'capsule',
                'manufacturer': 'XYZ Pharma',
                'strength': '250mg',
                'quantity_in_stock': 50,
                'unit': 'piece',
                'price_per_unit': 5.00,
                'minimum_stock_level': 15,
            },
            {
                'name': 'Cough Syrup',
                'generic_name': 'Dextromethorphan',
                'category': 'syrup',
                'manufacturer': 'MediCare',
                'strength': '100ml',
                'quantity_in_stock': 25,
                'unit': 'bottle',
                'price_per_unit': 45.00,
                'minimum_stock_level': 10,
            },
            {
                'name': 'Insulin',
                'generic_name': 'Human Insulin',
                'category': 'injection',
                'manufacturer': 'DiabCare',
                'strength': '10ml',
                'quantity_in_stock': 15,
                'unit': 'vial',
                'price_per_unit': 250.00,
                'minimum_stock_level': 5,
            },
            {
                'name': 'Aspirin',
                'generic_name': 'Acetylsalicylic Acid',
                'category': 'tablet',
                'manufacturer': 'HeartCare',
                'strength': '75mg',
                'quantity_in_stock': 8,  # Low stock
                'unit': 'piece',
                'price_per_unit': 1.50,
                'minimum_stock_level': 25,
            },
            {
                'name': 'Vitamin D3',
                'generic_name': 'Cholecalciferol',
                'category': 'capsule',
                'manufacturer': 'VitaHealth',
                'strength': '1000IU',
                'quantity_in_stock': 0,  # Out of stock
                'unit': 'piece',
                'price_per_unit': 3.00,
                'minimum_stock_level': 20,
            },
            {
                'name': 'Eye Drops',
                'generic_name': 'Artificial Tears',
                'category': 'drops',
                'manufacturer': 'EyeCare',
                'strength': '10ml',
                'quantity_in_stock': 30,
                'unit': 'bottle',
                'price_per_unit': 25.00,
                'minimum_stock_level': 10,
            },
            {
                'name': 'Antiseptic Cream',
                'generic_name': 'Povidone Iodine',
                'category': 'ointment',
                'manufacturer': 'SkinCare',
                'strength': '20g',
                'quantity_in_stock': 40,
                'unit': 'tube',
                'price_per_unit': 15.00,
                'minimum_stock_level': 12,
            },
        ]

        created_count = 0
        for medicine_data in sample_medicines:
            medicine_name = medicine_data['name']
            
            # Check if medicine already exists
            if Medicine.objects.filter(name=medicine_name, strength=medicine_data['strength']).exists():
                self.stdout.write(self.style.WARNING(f'Medicine "{medicine_name}" already exists'))
                continue
            
            # Create medicine
            medicine = Medicine.objects.create(
                created_by=pharmacy_user,
                expiry_date=date.today() + timedelta(days=365),  # 1 year from now
                **medicine_data
            )
            
            # Create initial stock transaction if there's stock
            if medicine.quantity_in_stock > 0:
                StockTransaction.objects.create(
                    medicine=medicine,
                    transaction_type='in',
                    quantity=medicine.quantity_in_stock,
                    reason='Initial stock - Sample data',
                    created_by=pharmacy_user
                )
            
            created_count += 1
            self.stdout.write(self.style.SUCCESS(f'Created medicine: {medicine_name}'))

        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} sample medicines!'))
        self.stdout.write('Sample medicines include:')
        self.stdout.write('- Various categories (tablets, capsules, syrups, injections, etc.)')
        self.stdout.write('- Different stock levels (including low stock and out of stock items)')
        self.stdout.write('- Realistic pricing and minimum stock levels')
