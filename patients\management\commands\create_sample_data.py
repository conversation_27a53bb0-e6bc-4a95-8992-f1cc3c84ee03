from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from patients.models import Patient
from medicines.models import Medicine
from prescriptions.models import Prescription, PrescriptionMedicine
from datetime import date, timedelta
import random

User = get_user_model()

class Command(BaseCommand):
    help = 'Create sample patients and prescriptions for Tahir Hospital'

    def handle(self, *args, **options):
        # Get users
        try:
            receptionist = User.objects.get(role='receptionist')
            dr_kashif = User.objects.get(username='dr_kashif')
            dr_tahir = User.objects.get(username='dr_tahir')
            doctors = [dr_kashif, dr_tahir]
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('Required users not found. Please create users first.'))
            return

        # Sample patient data
        sample_patients = [
            {
                'name': '<PERSON>',
                'age': 35,
                'gender': 'M',
                'contact': '+92-***********',
                'address': 'House 123, Block A, Lahore',
                'symptoms': 'Fever, headache, and body aches for 3 days. Patient reports temperature up to 102°F.',
                'assigned_doctor': dr_kashif,
            },
            {
                'name': '<PERSON><PERSON>',
                'age': 28,
                'gender': 'F',
                'contact': '+92-***********',
                'address': 'Flat 45, DHA Phase 2, Karachi',
                'symptoms': 'Persistent cough with phlegm, chest congestion, and difficulty breathing.',
                'assigned_doctor': dr_tahir,
            },
            {
                'name': 'Muhammad Hassan',
                'age': 45,
                'gender': 'M',
                'contact': '+92-***********',
                'address': 'Street 7, F-8/2, Islamabad',
                'symptoms': 'Severe stomach pain, nausea, and vomiting after meals. Pain in upper abdomen.',
                'assigned_doctor': dr_kashif,
            },
            {
                'name': 'Aisha Malik',
                'age': 22,
                'gender': 'F',
                'contact': '+92-303-4567890',
                'address': 'House 67, Model Town, Lahore',
                'symptoms': 'Skin rash on arms and legs, itching, and redness. Started 2 days ago.',
                'assigned_doctor': dr_tahir,
            },
            {
                'name': 'Omar Sheikh',
                'age': 60,
                'gender': 'M',
                'contact': '+92-***********',
                'address': 'Villa 12, Clifton, Karachi',
                'symptoms': 'High blood pressure, dizziness, and frequent headaches. Family history of hypertension.',
                'assigned_doctor': dr_kashif,
            },
        ]

        created_patients = []
        for patient_data in sample_patients:
            patient_name = patient_data['name']
            
            # Check if patient already exists
            if Patient.objects.filter(name=patient_name).exists():
                self.stdout.write(self.style.WARNING(f'Patient "{patient_name}" already exists'))
                continue
            
            # Create patient
            patient = Patient.objects.create(
                registered_by=receptionist,
                **patient_data
            )
            created_patients.append(patient)
            self.stdout.write(self.style.SUCCESS(f'Created patient: {patient_name} ({patient.patient_id})'))

        # Create sample prescriptions
        if created_patients:
            medicines = list(Medicine.objects.filter(is_active=True, quantity_in_stock__gt=0))
            
            if not medicines:
                self.stdout.write(self.style.WARNING('No medicines available. Please create medicines first.'))
                return

            sample_prescriptions = [
                {
                    'patient': created_patients[0],  # Ahmed Ali
                    'doctor': dr_kashif,
                    'diagnosis': 'Viral Fever with body aches',
                    'symptoms': 'High fever, headache, body pain',
                    'notes': 'Patient should rest and take plenty of fluids. Follow up if fever persists.',
                    'medicines': [
                        {'name': 'Paracetamol', 'quantity': 10, 'dosage': '1 tablet twice daily', 'duration': '5 days'},
                        {'name': 'Aspirin', 'quantity': 5, 'dosage': '1 tablet once daily', 'duration': '5 days'},
                    ]
                },
                {
                    'patient': created_patients[1],  # Fatima Khan
                    'doctor': dr_tahir,
                    'diagnosis': 'Respiratory tract infection',
                    'symptoms': 'Persistent cough, chest congestion',
                    'notes': 'Complete the full course of antibiotics. Use steam inhalation.',
                    'medicines': [
                        {'name': 'Amoxicillin', 'quantity': 14, 'dosage': '1 capsule twice daily', 'duration': '7 days'},
                        {'name': 'Cough Syrup', 'quantity': 1, 'dosage': '2 teaspoons thrice daily', 'duration': '7 days'},
                    ]
                },
                {
                    'patient': created_patients[2],  # Muhammad Hassan
                    'doctor': dr_kashif,
                    'diagnosis': 'Gastritis and acid reflux',
                    'symptoms': 'Stomach pain, nausea, vomiting',
                    'notes': 'Avoid spicy and oily foods. Take medicines before meals.',
                    'medicines': [
                        {'name': 'Paracetamol', 'quantity': 6, 'dosage': '1 tablet when needed', 'duration': '3 days'},
                    ]
                },
            ]

            for prescription_data in sample_prescriptions:
                medicines_data = prescription_data.pop('medicines')
                
                # Create prescription
                prescription = Prescription.objects.create(**prescription_data)
                
                # Add medicines to prescription
                for med_data in medicines_data:
                    try:
                        medicine = Medicine.objects.get(name=med_data['name'])
                        PrescriptionMedicine.objects.create(
                            prescription=prescription,
                            medicine=medicine,
                            quantity=med_data['quantity'],
                            dosage=med_data['dosage'],
                            duration=med_data['duration'],
                        )
                    except Medicine.DoesNotExist:
                        self.stdout.write(self.style.WARNING(f'Medicine "{med_data["name"]}" not found'))
                
                # Update patient status
                prescription.patient.status = 'prescribed'
                prescription.patient.save()
                
                self.stdout.write(self.style.SUCCESS(f'Created prescription: {prescription.prescription_id}'))

        self.stdout.write(self.style.SUCCESS('Sample data creation completed!'))
        self.stdout.write('Created:')
        self.stdout.write(f'- {len(created_patients)} patients')
        self.stdout.write('- 3 prescriptions with medicines')
        self.stdout.write('- Various patient statuses and medical conditions')
