from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.http import JsonResponse
from .models import Medicine, StockTransaction

@login_required
def medicine_list(request):
    """List all medicines with search and filter functionality"""
    if not (request.user.is_pharmacy or request.user.is_admin):
        messages.error(request, 'You do not have permission to view medicines.')
        return redirect('dashboard')

    medicines = Medicine.objects.filter(is_active=True)

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        medicines = medicines.filter(
            Q(name__icontains=search_query) |
            Q(generic_name__icontains=search_query) |
            Q(manufacturer__icontains=search_query)
        )

    # Filter by category
    category_filter = request.GET.get('category', '')
    if category_filter:
        medicines = medicines.filter(category=category_filter)

    # Filter by stock status
    stock_filter = request.GET.get('stock', '')
    if stock_filter == 'low':
        medicines = [m for m in medicines if m.is_low_stock]
    elif stock_filter == 'out':
        medicines = medicines.filter(quantity_in_stock=0)

    # Pagination
    paginator = Paginator(medicines, 15)  # Show 15 medicines per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_medicines = Medicine.objects.filter(is_active=True).count()
    low_stock_count = len([m for m in Medicine.objects.filter(is_active=True) if m.is_low_stock])
    out_of_stock_count = Medicine.objects.filter(is_active=True, quantity_in_stock=0).count()
    total_value = Medicine.objects.filter(is_active=True).aggregate(
        total=Sum('quantity_in_stock') * Sum('price_per_unit')
    )['total'] or 0

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'category_filter': category_filter,
        'stock_filter': stock_filter,
        'category_choices': Medicine.CATEGORY_CHOICES,
        'total_medicines': total_medicines,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'total_value': total_value,
    }
    return render(request, 'medicines/list.html', context)

@login_required
def add_medicine(request):
    """Add a new medicine to inventory"""
    if not (request.user.is_pharmacy or request.user.is_admin):
        messages.error(request, 'You do not have permission to add medicines.')
        return redirect('dashboard')

    if request.method == 'POST':
        try:
            medicine = Medicine.objects.create(
                name=request.POST.get('name'),
                generic_name=request.POST.get('generic_name', ''),
                category=request.POST.get('category'),
                manufacturer=request.POST.get('manufacturer', ''),
                strength=request.POST.get('strength'),
                quantity_in_stock=int(request.POST.get('quantity_in_stock', 0)),
                unit=request.POST.get('unit'),
                price_per_unit=float(request.POST.get('price_per_unit')),
                minimum_stock_level=int(request.POST.get('minimum_stock_level', 10)),
                expiry_date=request.POST.get('expiry_date') or None,
                created_by=request.user
            )

            # Create stock transaction
            if medicine.quantity_in_stock > 0:
                StockTransaction.objects.create(
                    medicine=medicine,
                    transaction_type='in',
                    quantity=medicine.quantity_in_stock,
                    reason='Initial stock',
                    created_by=request.user
                )

            messages.success(request, f'Medicine "{medicine.name}" added successfully.')
            return redirect('medicine_list')

        except Exception as e:
            messages.error(request, f'Error adding medicine: {str(e)}')

    context = {
        'category_choices': Medicine.CATEGORY_CHOICES,
        'unit_choices': Medicine.UNIT_CHOICES,
    }
    return render(request, 'medicines/add.html', context)

@login_required
def update_stock(request, medicine_id):
    """Update medicine stock"""
    if not (request.user.is_pharmacy or request.user.is_admin):
        messages.error(request, 'You do not have permission to update stock.')
        return redirect('dashboard')

    medicine = get_object_or_404(Medicine, id=medicine_id)

    if request.method == 'POST':
        try:
            transaction_type = request.POST.get('transaction_type')
            quantity = int(request.POST.get('quantity'))
            reason = request.POST.get('reason')

            old_stock = medicine.quantity_in_stock

            if transaction_type == 'in':
                medicine.add_stock(quantity)
            elif transaction_type == 'out':
                if not medicine.reduce_stock(quantity):
                    messages.error(request, 'Insufficient stock available.')
                    return redirect('medicine_list')
            elif transaction_type == 'adjustment':
                medicine.quantity_in_stock = quantity
                medicine.save()
                quantity = quantity - old_stock  # Calculate the difference

            # Create stock transaction
            StockTransaction.objects.create(
                medicine=medicine,
                transaction_type=transaction_type,
                quantity=quantity if transaction_type != 'out' else -quantity,
                reason=reason,
                created_by=request.user
            )

            messages.success(request, f'Stock updated for "{medicine.name}".')
            return redirect('medicine_list')

        except Exception as e:
            messages.error(request, f'Error updating stock: {str(e)}')

    context = {
        'medicine': medicine,
        'transaction_types': StockTransaction.TRANSACTION_TYPES,
    }
    return render(request, 'medicines/update_stock.html', context)
