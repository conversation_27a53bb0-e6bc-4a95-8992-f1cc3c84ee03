from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.http import JsonResponse
from .models import Medicine, StockTransaction
from decimal import Decimal

@login_required
def store_dashboard(request):
    """Store Management Dashboard"""
    if not (request.user.is_store_manager or request.user.is_admin):
        messages.error(request, 'You do not have permission to access the store.')
        return redirect('dashboard')
    
    # Store Statistics
    total_medicines = Medicine.objects.filter(is_active=True).count()
    low_stock_medicines = [m for m in Medicine.objects.filter(is_active=True) if m.is_low_stock]
    out_of_stock_count = Medicine.objects.filter(is_active=True, quantity_in_stock=0).count()
    
    # Calculate total inventory value
    total_value = sum(m.total_value for m in Medicine.objects.filter(is_active=True))
    
    # Recent stock transactions
    recent_transactions = StockTransaction.objects.select_related('medicine', 'created_by').order_by('-created_at')[:10]
    
    # Low stock alerts
    low_stock_alerts = low_stock_medicines[:5]
    
    context = {
        'total_medicines': total_medicines,
        'low_stock_count': len(low_stock_medicines),
        'out_of_stock_count': out_of_stock_count,
        'total_value': total_value,
        'recent_transactions': recent_transactions,
        'low_stock_alerts': low_stock_alerts,
    }
    return render(request, 'medicines/store_dashboard.html', context)

@login_required
def store_inventory(request):
    """Store Inventory Management"""
    if not (request.user.is_store_manager or request.user.is_admin):
        messages.error(request, 'You do not have permission to access the store.')
        return redirect('dashboard')
    
    medicines = Medicine.objects.filter(is_active=True)
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        medicines = medicines.filter(
            Q(name__icontains=search_query) |
            Q(generic_name__icontains=search_query) |
            Q(manufacturer__icontains=search_query)
        )
    
    # Filter by category
    category_filter = request.GET.get('category', '')
    if category_filter:
        medicines = medicines.filter(category=category_filter)
    
    # Filter by stock status
    stock_filter = request.GET.get('stock', '')
    if stock_filter == 'low':
        medicines = [m for m in medicines if m.is_low_stock]
    elif stock_filter == 'out':
        medicines = medicines.filter(quantity_in_stock=0)
    
    # Pagination
    paginator = Paginator(medicines, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'category_filter': category_filter,
        'stock_filter': stock_filter,
        'category_choices': Medicine.CATEGORY_CHOICES,
    }
    return render(request, 'medicines/store_inventory.html', context)

@login_required
def add_medicine_to_store(request):
    """Add new medicine to store"""
    if not (request.user.is_store_manager or request.user.is_admin):
        messages.error(request, 'You do not have permission to add medicines to store.')
        return redirect('dashboard')
    
    if request.method == 'POST':
        try:
            medicine = Medicine.objects.create(
                name=request.POST.get('name'),
                generic_name=request.POST.get('generic_name', ''),
                category=request.POST.get('category'),
                manufacturer=request.POST.get('manufacturer', ''),
                strength=request.POST.get('strength'),
                quantity_in_stock=int(request.POST.get('quantity_in_stock', 0)),
                unit=request.POST.get('unit'),
                price_per_unit=Decimal(request.POST.get('price_per_unit')),
                minimum_stock_level=int(request.POST.get('minimum_stock_level', 10)),
                expiry_date=request.POST.get('expiry_date') or None,
                created_by=request.user
            )
            
            # Create stock transaction for initial stock
            if medicine.quantity_in_stock > 0:
                StockTransaction.objects.create(
                    medicine=medicine,
                    transaction_type='in',
                    quantity=medicine.quantity_in_stock,
                    reason='Initial stock - New medicine added to store',
                    created_by=request.user
                )
            
            messages.success(request, f'Medicine "{medicine.name}" added to store successfully.')
            return redirect('store:inventory')
            
        except Exception as e:
            messages.error(request, f'Error adding medicine to store: {str(e)}')
    
    context = {
        'category_choices': Medicine.CATEGORY_CHOICES,
        'unit_choices': Medicine.UNIT_CHOICES,
    }
    return render(request, 'medicines/add_to_store.html', context)

@login_required
def restock_medicine(request, medicine_id):
    """Restock medicine in store"""
    if not (request.user.is_store_manager or request.user.is_admin):
        messages.error(request, 'You do not have permission to restock medicines.')
        return redirect('dashboard')
    
    medicine = get_object_or_404(Medicine, id=medicine_id)
    
    if request.method == 'POST':
        try:
            quantity = int(request.POST.get('quantity'))
            reason = request.POST.get('reason', 'Stock replenishment')
            
            if quantity > 0:
                # Add stock
                medicine.add_stock(quantity)
                
                # Create stock transaction
                StockTransaction.objects.create(
                    medicine=medicine,
                    transaction_type='in',
                    quantity=quantity,
                    reason=reason,
                    created_by=request.user
                )
                
                messages.success(request, f'Added {quantity} {medicine.get_unit_display()} of {medicine.name} to store.')
            else:
                messages.error(request, 'Quantity must be greater than 0.')
                
            return redirect('store:inventory')
            
        except Exception as e:
            messages.error(request, f'Error restocking medicine: {str(e)}')
    
    context = {
        'medicine': medicine,
    }
    return render(request, 'medicines/restock.html', context)

@login_required
def stock_transactions(request):
    """View all stock transactions"""
    if not (request.user.is_store_manager or request.user.is_admin):
        messages.error(request, 'You do not have permission to view stock transactions.')
        return redirect('dashboard')
    
    transactions = StockTransaction.objects.select_related('medicine', 'created_by').order_by('-created_at')
    
    # Filter by transaction type
    transaction_filter = request.GET.get('type', '')
    if transaction_filter:
        transactions = transactions.filter(transaction_type=transaction_filter)
    
    # Filter by medicine
    medicine_filter = request.GET.get('medicine', '')
    if medicine_filter:
        transactions = transactions.filter(medicine__name__icontains=medicine_filter)
    
    # Pagination
    paginator = Paginator(transactions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'transaction_filter': transaction_filter,
        'medicine_filter': medicine_filter,
        'transaction_types': StockTransaction.TRANSACTION_TYPES,
    }
    return render(request, 'medicines/stock_transactions.html', context)

@login_required
def delete_medicine(request, medicine_id):
    """Delete medicine from store"""
    if not (request.user.is_store_manager or request.user.is_admin):
        messages.error(request, 'You do not have permission to delete medicines.')
        return redirect('dashboard')

    medicine = get_object_or_404(Medicine, id=medicine_id)

    # Check if medicine is used in any prescriptions
    from prescriptions.models import PrescriptionMedicine
    prescription_count = PrescriptionMedicine.objects.filter(medicine=medicine).count()

    if request.method == 'POST':
        if prescription_count > 0:
            messages.error(request, f'Cannot delete {medicine.name}. It is used in {prescription_count} prescription(s).')
            return redirect('store:inventory')

        try:
            medicine_name = medicine.name

            # Delete related stock transactions first
            StockTransaction.objects.filter(medicine=medicine).delete()

            # Delete the medicine
            medicine.delete()

            messages.success(request, f'Medicine "{medicine_name}" has been deleted successfully.')
            return redirect('store:inventory')

        except Exception as e:
            messages.error(request, f'Error deleting medicine: {str(e)}')
            return redirect('store:inventory')

    context = {
        'medicine': medicine,
        'prescription_count': prescription_count,
        'can_delete': prescription_count == 0,
    }
    return render(request, 'medicines/delete_medicine.html', context)
