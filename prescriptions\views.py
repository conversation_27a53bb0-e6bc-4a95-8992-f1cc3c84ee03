from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.utils import timezone
from patients.models import Patient
from medicines.models import Medicine, StockTransaction
from .models import Prescription, PrescriptionMedicine

@login_required
def create_prescription(request, patient_id):
    """Create a new prescription for a patient (Doctor only)"""
    if not (request.user.is_doctor or request.user.is_admin):
        messages.error(request, 'You do not have permission to create prescriptions.')
        return redirect('dashboard')

    patient = get_object_or_404(Patient, patient_id=patient_id)

    # Check if doctor is assigned to this patient or is admin
    if request.user.is_doctor and patient.assigned_doctor != request.user:
        messages.error(request, 'You can only create prescriptions for your assigned patients.')
        return redirect('patient_list')

    if request.method == 'POST':
        try:
            # Create prescription
            prescription = Prescription.objects.create(
                patient=patient,
                doctor=request.user,
                diagnosis=request.POST.get('diagnosis'),
                symptoms=request.POST.get('symptoms', ''),
                notes=request.POST.get('notes', '')
            )

            # Add medicines to prescription
            medicine_ids = request.POST.getlist('medicine_id[]')
            quantities = request.POST.getlist('quantity[]')
            dosages = request.POST.getlist('dosage[]')
            durations = request.POST.getlist('duration[]')
            instructions_list = request.POST.getlist('instructions[]')

            for i, medicine_id in enumerate(medicine_ids):
                if medicine_id and quantities[i]:
                    medicine = Medicine.objects.get(id=medicine_id)
                    PrescriptionMedicine.objects.create(
                        prescription=prescription,
                        medicine=medicine,
                        quantity=int(quantities[i]),
                        dosage=dosages[i] if i < len(dosages) else '',
                        duration=durations[i] if i < len(durations) else '',
                        instructions=instructions_list[i] if i < len(instructions_list) else ''
                    )

            # Update patient status
            patient.status = 'prescribed'
            patient.save()

            messages.success(request, f'Prescription {prescription.prescription_id} created successfully.')
            return redirect('prescription_detail', prescription.prescription_id)

        except Exception as e:
            messages.error(request, f'Error creating prescription: {str(e)}')

    # Get available medicines
    medicines = Medicine.objects.filter(is_active=True, quantity_in_stock__gt=0).order_by('name')

    context = {
        'patient': patient,
        'medicines': medicines,
    }
    return render(request, 'prescriptions/create.html', context)

@login_required
def prescription_list(request):
    """List prescriptions based on user role"""
    prescriptions = Prescription.objects.all().select_related('patient', 'doctor', 'dispensed_by')

    # Filter based on user role
    if request.user.is_doctor:
        prescriptions = prescriptions.filter(doctor=request.user)
    elif request.user.is_pharmacy:
        # Pharmacy sees all prescriptions
        pass
    elif request.user.is_receptionist:
        # Receptionist sees all prescriptions
        pass

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        prescriptions = prescriptions.filter(
            Q(prescription_id__icontains=search_query) |
            Q(patient__name__icontains=search_query) |
            Q(patient__patient_id__icontains=search_query) |
            Q(doctor__first_name__icontains=search_query) |
            Q(doctor__last_name__icontains=search_query)
        )

    # Filter by status
    status_filter = request.GET.get('status', '')
    if status_filter:
        prescriptions = prescriptions.filter(status=status_filter)

    # Pagination
    paginator = Paginator(prescriptions, 10)  # Show 10 prescriptions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_choices': Prescription.STATUS_CHOICES,
    }
    return render(request, 'prescriptions/list.html', context)

@login_required
def prescription_detail(request, prescription_id):
    """View prescription details"""
    prescription = get_object_or_404(Prescription, prescription_id=prescription_id)

    # Check permissions
    if request.user.is_doctor and prescription.doctor != request.user:
        messages.error(request, 'You can only view your own prescriptions.')
        return redirect('prescription_list')

    context = {
        'prescription': prescription,
        'prescription_medicines': prescription.prescription_medicines.all().select_related('medicine'),
    }
    return render(request, 'prescriptions/detail.html', context)

@login_required
def dispense_prescription(request, prescription_id):
    """Dispense medicines for a prescription (Pharmacy only)"""
    if not (request.user.is_pharmacy or request.user.is_admin):
        messages.error(request, 'You do not have permission to dispense medicines.')
        return redirect('dashboard')

    prescription = get_object_or_404(Prescription, prescription_id=prescription_id)

    if prescription.status == 'dispensed':
        messages.warning(request, 'This prescription has already been fully dispensed.')
        return redirect('prescription_detail', prescription.prescription_id)

    if request.method == 'POST':
        try:
            all_dispensed = True

            # Process each medicine in the prescription
            for prescription_medicine in prescription.prescription_medicines.all():
                quantity_to_dispense = int(request.POST.get(f'quantity_{prescription_medicine.id}', 0))

                if quantity_to_dispense > 0:
                    # Check if enough stock is available
                    if prescription_medicine.medicine.quantity_in_stock < quantity_to_dispense:
                        messages.error(request, f'Insufficient stock for {prescription_medicine.medicine.name}. Available: {prescription_medicine.medicine.quantity_in_stock}')
                        return redirect('dispense_prescription', prescription.prescription_id)

                    # Check if not dispensing more than prescribed
                    remaining = prescription_medicine.remaining_quantity
                    if quantity_to_dispense > remaining:
                        messages.error(request, f'Cannot dispense more than prescribed for {prescription_medicine.medicine.name}. Remaining: {remaining}')
                        return redirect('dispense_prescription', prescription.prescription_id)

                    # Reduce stock and update dispensed quantity
                    prescription_medicine.medicine.reduce_stock(quantity_to_dispense)
                    prescription_medicine.quantity_dispensed += quantity_to_dispense
                    prescription_medicine.save()

                    # Create stock transaction
                    StockTransaction.objects.create(
                        medicine=prescription_medicine.medicine,
                        transaction_type='out',
                        quantity=-quantity_to_dispense,  # Negative for stock out
                        reason=f'Dispensed for prescription {prescription.prescription_id}',
                        reference=prescription.prescription_id,
                        created_by=request.user
                    )

                # Check if this medicine is fully dispensed
                if not prescription_medicine.is_fully_dispensed:
                    all_dispensed = False

            # Update prescription status
            if all_dispensed:
                prescription.status = 'dispensed'
                prescription.dispensed_at = timezone.now()
                prescription.dispensed_by = request.user
                # Update patient status
                prescription.patient.status = 'completed'
                prescription.patient.save()
            else:
                prescription.status = 'partially_dispensed'

            prescription.save()

            messages.success(request, f'Prescription {prescription.prescription_id} has been {"fully" if all_dispensed else "partially"} dispensed.')
            return redirect('prescription_detail', prescription.prescription_id)

        except Exception as e:
            messages.error(request, f'Error dispensing prescription: {str(e)}')

    context = {
        'prescription': prescription,
        'prescription_medicines': prescription.prescription_medicines.all().select_related('medicine'),
    }
    return render(request, 'prescriptions/dispense.html', context)

@login_required
def pharmacy_dashboard(request):
    """Pharmacy dashboard with pending prescriptions and stock alerts"""
    if not (request.user.is_pharmacy or request.user.is_admin):
        messages.error(request, 'You do not have permission to access pharmacy dashboard.')
        return redirect('dashboard')

    # Get pending prescriptions
    pending_prescriptions = Prescription.objects.filter(
        status__in=['pending', 'partially_dispensed']
    ).select_related('patient', 'doctor').order_by('-created_at')[:10]

    # Get low stock medicines
    from medicines.models import Medicine
    low_stock_medicines = [m for m in Medicine.objects.filter(is_active=True) if m.is_low_stock][:10]

    # Get out of stock medicines
    out_of_stock_medicines = Medicine.objects.filter(is_active=True, quantity_in_stock=0)[:10]

    # Statistics
    total_pending = Prescription.objects.filter(status__in=['pending', 'partially_dispensed']).count()
    dispensed_today = Prescription.objects.filter(
        status='dispensed',
        dispensed_at__date=timezone.now().date()
    ).count()

    context = {
        'pending_prescriptions': pending_prescriptions,
        'low_stock_medicines': low_stock_medicines,
        'out_of_stock_medicines': out_of_stock_medicines,
        'total_pending': total_pending,
        'dispensed_today': dispensed_today,
        'low_stock_count': len(low_stock_medicines),
        'out_of_stock_count': out_of_stock_medicines.count(),
    }
    return render(request, 'prescriptions/pharmacy_dashboard.html', context)
