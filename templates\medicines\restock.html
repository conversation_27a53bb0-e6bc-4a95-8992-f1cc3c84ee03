{% extends 'base.html' %}

{% block title %}Restock Medicine - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-plus"></i> Restock Medicine</h1>
        <a href="{% url 'store:inventory' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Inventory
        </a>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-boxes"></i> Add Stock</h5>
                </div>
                <div class="card-body">
                    <!-- Medicine Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Medicine Details:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Name:</th>
                                    <td><strong>{{ medicine.name }}</strong></td>
                                </tr>
                                {% if medicine.generic_name %}
                                <tr>
                                    <th>Generic Name:</th>
                                    <td>{{ medicine.generic_name }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>Strength:</th>
                                    <td>{{ medicine.strength }}</td>
                                </tr>
                                <tr>
                                    <th>Category:</th>
                                    <td>{{ medicine.get_category_display }}</td>
                                </tr>
                                {% if medicine.manufacturer %}
                                <tr>
                                    <th>Manufacturer:</th>
                                    <td>{{ medicine.manufacturer }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Current Stock Status:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Current Stock:</th>
                                    <td>
                                        <strong>{{ medicine.quantity_in_stock }}</strong> {{ medicine.get_unit_display }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Minimum Level:</th>
                                    <td>{{ medicine.minimum_stock_level }} {{ medicine.get_unit_display }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        {% if medicine.quantity_in_stock == 0 %}
                                            <span class="badge bg-danger">Out of Stock</span>
                                        {% elif medicine.is_low_stock %}
                                            <span class="badge bg-warning">Low Stock</span>
                                        {% else %}
                                            <span class="badge bg-success">In Stock</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Price per Unit:</th>
                                    <td>₨{{ medicine.price_per_unit }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Restock Form -->
                    <hr>
                    <h6 class="mb-3">Add Stock</h6>
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">Quantity to Add *</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           min="1" required autofocus>
                                    <small class="text-muted">Unit: {{ medicine.get_unit_display }}</small>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="reason" class="form-label">Reason for Restocking</label>
                                    <input type="text" class="form-control" id="reason" name="reason" 
                                           value="Stock replenishment" placeholder="Enter reason for adding stock">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Stock Calculation Preview -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-calculator"></i> Stock Calculation Preview</h6>
                            <p class="mb-0">
                                Current Stock: <strong>{{ medicine.quantity_in_stock }}</strong> {{ medicine.get_unit_display }}
                                <br>
                                After Adding: <strong id="newStock">{{ medicine.quantity_in_stock }}</strong> {{ medicine.get_unit_display }}
                                <br>
                                Estimated Value: <strong>₨<span id="estimatedValue">0.00</span></strong>
                            </p>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'store:inventory' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Stock
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Calculate new stock and value in real-time
document.getElementById('quantity').addEventListener('input', function() {
    const currentStock = {{ medicine.quantity_in_stock }};
    const pricePerUnit = {{ medicine.price_per_unit }};
    const addQuantity = parseInt(this.value) || 0;
    
    const newStock = currentStock + addQuantity;
    const estimatedValue = addQuantity * pricePerUnit;
    
    document.getElementById('newStock').textContent = newStock;
    document.getElementById('estimatedValue').textContent = estimatedValue.toFixed(2);
});
</script>
{% endblock %}
