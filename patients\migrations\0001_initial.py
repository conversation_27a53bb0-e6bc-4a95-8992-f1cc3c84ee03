# Generated by Django 5.2.4 on 2025-08-01 12:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Patient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('patient_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('age', models.PositiveIntegerField()),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1)),
                ('contact', models.Char<PERSON>ield(max_length=15)),
                ('address', models.TextField(blank=True, null=True)),
                ('symptoms', models.TextField()),
                ('status', models.CharField(choices=[('registered', 'Registered'), ('consulting', 'Consulting'), ('prescribed', 'Prescribed'), ('completed', 'Completed')], default='registered', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_doctor', models.ForeignKey(blank=True, limit_choices_to={'role': 'doctor'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_patients', to=settings.AUTH_USER_MODEL)),
                ('registered_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='registered_patients', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
