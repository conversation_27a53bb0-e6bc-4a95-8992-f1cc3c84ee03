from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Create initial users for Tahir Hospital'

    def handle(self, *args, **options):
        # Create admin user
        if not User.objects.filter(username='admin').exists():
            admin = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='Admin',
                last_name='User',
                role='admin',
                is_staff=True,
                is_superuser=True
            )
            self.stdout.write(self.style.SUCCESS(f'Admin user created: {admin.username}'))
        else:
            self.stdout.write(self.style.WARNING('Admin user already exists'))

        # Create receptionist
        if not User.objects.filter(username='receptionist').exists():
            receptionist = User.objects.create_user(
                username='receptionist',
                email='<EMAIL>',
                password='receptionist123',
                first_name='Reception',
                last_name='Staff',
                role='receptionist'
            )
            self.stdout.write(self.style.SUCCESS(f'Receptionist user created: {receptionist.username}'))
        else:
            self.stdout.write(self.style.WARNING('Receptionist user already exists'))

        # Create Doctor Kashif
        if not User.objects.filter(username='dr_kashif').exists():
            dr_kashif = User.objects.create_user(
                username='dr_kashif',
                email='<EMAIL>',
                password='doctor123',
                first_name='Dr. Kashif',
                last_name='',
                role='doctor',
                specialization='General Medicine'
            )
            self.stdout.write(self.style.SUCCESS(f'Doctor Kashif created: {dr_kashif.username}'))
        else:
            self.stdout.write(self.style.WARNING('Doctor Kashif already exists'))

        # Create Doctor Tahir
        if not User.objects.filter(username='dr_tahir').exists():
            dr_tahir = User.objects.create_user(
                username='dr_tahir',
                email='<EMAIL>',
                password='doctor123',
                first_name='Dr. Tahir',
                last_name='',
                role='doctor',
                specialization='Homeopathic Doctor and Nutritionist'
            )
            self.stdout.write(self.style.SUCCESS(f'Doctor Tahir created: {dr_tahir.username}'))
        else:
            self.stdout.write(self.style.WARNING('Doctor Tahir already exists'))

        # Create pharmacy user
        if not User.objects.filter(username='pharmacy').exists():
            pharmacy = User.objects.create_user(
                username='pharmacy',
                email='<EMAIL>',
                password='pharmacy123',
                first_name='Pharmacy',
                last_name='Staff',
                role='pharmacy'
            )
            self.stdout.write(self.style.SUCCESS(f'Pharmacy user created: {pharmacy.username}'))
        else:
            self.stdout.write(self.style.WARNING('Pharmacy user already exists'))

        # Create store manager user
        if not User.objects.filter(username='store_manager').exists():
            store_manager = User.objects.create_user(
                username='store_manager',
                email='<EMAIL>',
                password='store123',
                first_name='Store',
                last_name='Manager',
                role='store_manager'
            )
            self.stdout.write(self.style.SUCCESS(f'Store Manager created: {store_manager.username}'))
        else:
            self.stdout.write(self.style.WARNING('Store Manager already exists'))

        self.stdout.write(self.style.SUCCESS('Initial users setup completed!'))
        self.stdout.write(self.style.SUCCESS('Login credentials:'))
        self.stdout.write('Admin: admin / admin123')
        self.stdout.write('Receptionist: receptionist / receptionist123')
        self.stdout.write('Doctor Kashif: dr_kashif / doctor123')
        self.stdout.write('Doctor Tahir: dr_tahir / doctor123')
        self.stdout.write('Pharmacy: pharmacy / pharmacy123')
        self.stdout.write('Store Manager: store_manager / store123')
