from django.contrib import admin
from .models import DailyTokenCounter, PatientToken

@admin.register(DailyTokenCounter)
class DailyTokenCounterAdmin(admin.ModelAdmin):
    list_display = ['date', 'current_token_number', 'created_at']
    list_filter = ['date']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-date']

@admin.register(PatientToken)
class PatientTokenAdmin(admin.ModelAdmin):
    list_display = ['formatted_token_number', 'patient', 'token_date', 'generated_at', 'print_count']
    list_filter = ['token_date', 'generated_at', 'generated_by']
    search_fields = ['patient__name', 'patient__patient_id', 'token_number']
    readonly_fields = ['generated_at', 'printed_at']
    ordering = ['-generated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('patient', 'generated_by')
