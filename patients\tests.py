from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from .models import Patient

User = get_user_model()

class PatientModelTest(TestCase):
    """Test cases for the Patient model"""

    def setUp(self):
        self.receptionist = User.objects.create_user(
            username='receptionist',
            password='testpass123',
            role='receptionist'
        )
        self.doctor = User.objects.create_user(
            username='doctor',
            password='testpass123',
            role='doctor'
        )

    def test_create_patient(self):
        """Test creating a patient"""
        patient = Patient.objects.create(
            name='<PERSON>',
            age=30,
            gender='M',
            contact='+92-***********',
            symptoms='Fever and headache',
            registered_by=self.receptionist,
            assigned_doctor=self.doctor
        )

        self.assertEqual(patient.name, '<PERSON>')
        self.assertEqual(patient.age, 30)
        self.assertEqual(patient.gender, 'M')
        self.assertEqual(patient.status, 'registered')
        self.assertTrue(patient.patient_id.startswith('TH'))
        self.assertEqual(len(patient.patient_id), 6)  # TH + 4 digits

    def test_patient_id_generation(self):
        """Test automatic patient ID generation"""
        patient1 = Patient.objects.create(
            name='Patient 1',
            age=25,
            gender='F',
            contact='+92-***********',
            symptoms='Test symptoms',
            registered_by=self.receptionist
        )

        patient2 = Patient.objects.create(
            name='Patient 2',
            age=35,
            gender='M',
            contact='+92-***********',
            symptoms='Test symptoms',
            registered_by=self.receptionist
        )

        # Patient IDs should be sequential
        id1_num = int(patient1.patient_id[2:])
        id2_num = int(patient2.patient_id[2:])
        self.assertEqual(id2_num, id1_num + 1)

    def test_patient_age_group_property(self):
        """Test patient age group property"""
        child = Patient.objects.create(
            name='Child', age=10, gender='M', contact='123',
            symptoms='Test', registered_by=self.receptionist
        )
        adult = Patient.objects.create(
            name='Adult', age=30, gender='F', contact='456',
            symptoms='Test', registered_by=self.receptionist
        )
        senior = Patient.objects.create(
            name='Senior', age=70, gender='M', contact='789',
            symptoms='Test', registered_by=self.receptionist
        )

        self.assertEqual(child.age_group, 'Child')
        self.assertEqual(adult.age_group, 'Adult')
        self.assertEqual(senior.age_group, 'Senior')

    def test_patient_string_representation(self):
        """Test patient string representation"""
        patient = Patient.objects.create(
            name='Test Patient',
            age=25,
            gender='M',
            contact='123456789',
            symptoms='Test symptoms',
            registered_by=self.receptionist
        )
        expected = f"{patient.patient_id} - Test Patient"
        self.assertEqual(str(patient), expected)


class PatientViewsTest(TestCase):
    """Test cases for patient views"""

    def setUp(self):
        self.client = Client()
        self.receptionist = User.objects.create_user(
            username='receptionist',
            password='testpass123',
            role='receptionist'
        )
        self.doctor = User.objects.create_user(
            username='doctor',
            password='testpass123',
            role='doctor'
        )
        self.pharmacy = User.objects.create_user(
            username='pharmacy',
            password='testpass123',
            role='pharmacy'
        )

    def test_register_patient_requires_permission(self):
        """Test patient registration requires receptionist or admin role"""
        # Test with pharmacy user (should be denied)
        self.client.login(username='pharmacy', password='testpass123')
        response = self.client.get(reverse('register_patient'))
        # Pharmacy user gets redirected to pharmacy dashboard, then to dashboard
        self.assertEqual(response.status_code, 302)

        # Test with receptionist (should be allowed)
        self.client.login(username='receptionist', password='testpass123')
        response = self.client.get(reverse('register_patient'))
        self.assertEqual(response.status_code, 200)

    def test_register_patient_post(self):
        """Test patient registration POST request"""
        self.client.login(username='receptionist', password='testpass123')

        response = self.client.post(reverse('register_patient'), {
            'name': 'Test Patient',
            'age': 30,
            'gender': 'M',
            'contact': '+92-***********',
            'address': 'Test Address',
            'symptoms': 'Test symptoms',
            'assigned_doctor': self.doctor.id
        })

        self.assertRedirects(response, reverse('patient_list'))

        # Check if patient was created
        patient = Patient.objects.get(name='Test Patient')
        self.assertEqual(patient.age, 30)
        self.assertEqual(patient.assigned_doctor, self.doctor)
        self.assertEqual(patient.registered_by, self.receptionist)

    def test_patient_list_view(self):
        """Test patient list view"""
        # Create test patient
        patient = Patient.objects.create(
            name='Test Patient',
            age=25,
            gender='F',
            contact='123456789',
            symptoms='Test symptoms',
            registered_by=self.receptionist,
            assigned_doctor=self.doctor
        )

        self.client.login(username='receptionist', password='testpass123')
        response = self.client.get(reverse('patient_list'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Patient')
        self.assertContains(response, patient.patient_id)

    def test_patient_detail_view(self):
        """Test patient detail view"""
        patient = Patient.objects.create(
            name='Test Patient',
            age=25,
            gender='F',
            contact='123456789',
            symptoms='Test symptoms',
            registered_by=self.receptionist,
            assigned_doctor=self.doctor
        )

        self.client.login(username='doctor', password='testpass123')
        response = self.client.get(reverse('patient_detail', args=[patient.patient_id]))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Patient')
        self.assertContains(response, patient.patient_id)

    def test_delete_patient_requires_permission(self):
        """Test patient deletion requires receptionist or admin role"""
        patient = Patient.objects.create(
            name='Test Patient',
            age=25,
            gender='F',
            contact='123456789',
            symptoms='Test symptoms',
            registered_by=self.receptionist
        )

        # Test with doctor (should be denied)
        self.client.login(username='doctor', password='testpass123')
        response = self.client.get(reverse('delete_patient', args=[patient.patient_id]))
        self.assertEqual(response.status_code, 302)  # Redirected

        # Test with receptionist (should be allowed)
        self.client.login(username='receptionist', password='testpass123')
        response = self.client.get(reverse('delete_patient', args=[patient.patient_id]))
        self.assertEqual(response.status_code, 200)

    def test_delete_patient_without_prescriptions(self):
        """Test deleting patient without prescriptions"""
        patient = Patient.objects.create(
            name='Test Patient to Delete',
            age=25,
            gender='F',
            contact='123456789',
            symptoms='Test symptoms',
            registered_by=self.receptionist
        )

        self.client.login(username='receptionist', password='testpass123')

        # Test POST request to delete
        response = self.client.post(reverse('delete_patient', args=[patient.patient_id]))
        self.assertRedirects(response, reverse('patient_list'))

        # Check if patient was deleted
        self.assertFalse(Patient.objects.filter(patient_id=patient.patient_id).exists())

    def test_cannot_delete_patient_with_prescriptions(self):
        """Test that patients with prescriptions cannot be deleted"""
        from prescriptions.models import Prescription

        patient = Patient.objects.create(
            name='Test Patient with Prescription',
            age=25,
            gender='F',
            contact='123456789',
            symptoms='Test symptoms',
            registered_by=self.receptionist,
            assigned_doctor=self.doctor
        )

        # Create a prescription for the patient
        Prescription.objects.create(
            patient=patient,
            doctor=self.doctor,
            diagnosis='Test diagnosis'
        )

        self.client.login(username='receptionist', password='testpass123')

        # Try to delete - should be redirected back to patient detail
        response = self.client.post(reverse('delete_patient', args=[patient.patient_id]))
        self.assertRedirects(response, reverse('patient_detail', args=[patient.patient_id]))

        # Check if patient still exists
        self.assertTrue(Patient.objects.filter(patient_id=patient.patient_id).exists())
