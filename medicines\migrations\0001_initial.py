# Generated by Django 5.2.4 on 2025-08-01 12:09

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Medicine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('generic_name', models.CharField(blank=True, max_length=200, null=True)),
                ('category', models.CharField(choices=[('tablet', 'Tablet'), ('capsule', 'Capsule'), ('syrup', 'Syrup'), ('injection', 'Injection'), ('ointment', 'Ointment'), ('drops', 'Drops'), ('inhaler', 'Inhaler'), ('other', 'Other')], max_length=20)),
                ('manufacturer', models.CharField(blank=True, max_length=100, null=True)),
                ('strength', models.CharField(help_text='e.g., 500mg, 10ml', max_length=50)),
                ('quantity_in_stock', models.PositiveIntegerField(default=0)),
                ('unit', models.CharField(choices=[('piece', 'Piece'), ('bottle', 'Bottle'), ('vial', 'Vial'), ('tube', 'Tube'), ('pack', 'Pack'), ('box', 'Box')], default='piece', max_length=20)),
                ('price_per_unit', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('minimum_stock_level', models.PositiveIntegerField(default=10, help_text='Alert when stock falls below this level')),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_medicines', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
                'unique_together': {('name', 'strength')},
            },
        ),
        migrations.CreateModel(
            name='StockTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('in', 'Stock In'), ('out', 'Stock Out'), ('adjustment', 'Stock Adjustment'), ('expired', 'Expired Stock')], max_length=20)),
                ('quantity', models.IntegerField()),
                ('reason', models.CharField(max_length=200)),
                ('reference', models.CharField(blank=True, help_text='Prescription ID, Purchase Order, etc.', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('medicine', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_transactions', to='medicines.medicine')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
