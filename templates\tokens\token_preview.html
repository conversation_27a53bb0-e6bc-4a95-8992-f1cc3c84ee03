{% extends 'base.html' %}

{% block title %}Token Preview - {{ hospital_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-ticket-alt"></i> Patient Token Preview</h5>
                </div>
                <div class="card-body">
                    <!-- Token Preview (styled to look like thermal printer output) -->
                    <div class="token-preview" style="
                        font-family: 'Courier New', monospace;
                        background: #f8f9fa;
                        border: 2px dashed #dee2e6;
                        padding: 20px;
                        margin: 20px 0;
                        text-align: center;
                        max-width: 300px;
                        margin: 0 auto;
                        line-height: 1.4;
                    ">
                        <!-- Hospital Header -->
                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 5px;">
                            {{ hospital_name|upper }}
                        </div>
                        <div style="font-size: 12px; margin-bottom: 3px;">
                            {{ hospital_address }}
                        </div>
                        <div style="font-size: 12px; margin-bottom: 10px;">
                            Phone: {{ hospital_phone }}
                        </div>
                        <div style="border-top: 1px solid #333; margin: 10px 0;"></div>
                        
                        <!-- Token Number -->
                        <div style="font-size: 36px; font-weight: bold; margin: 15px 0;">
                            TOKEN #{{ token.formatted_token_number }}
                        </div>
                        
                        <!-- Date and Time -->
                        <div style="font-size: 14px; margin-bottom: 10px;">
                            {{ token.generated_at|date:"d/m/Y H:i" }}
                        </div>
                        <div style="border-top: 1px solid #333; margin: 10px 0;"></div>
                        
                        <!-- Patient Details -->
                        <div style="text-align: left; font-size: 12px;">
                            <div style="font-weight: bold; margin-bottom: 5px;">PATIENT DETAILS:</div>
                            <div>Name: {{ token.patient.name }}</div>
                            <div>Age: {{ token.patient.age }} years</div>
                            <div>Gender: {{ token.patient.get_gender_display }}</div>
                            <div>Contact: {{ token.patient.contact }}</div>
                            {% if token.patient.assigned_doctor %}
                            <div>Doctor: {{ token.patient.assigned_doctor.get_full_name }}</div>
                            {% endif %}
                        </div>
                        <div style="border-top: 1px solid #333; margin: 10px 0;"></div>
                        
                        <!-- Footer -->
                        <div style="font-size: 11px;">
                            <div>Please wait for your turn</div>
                            <div>Keep this token safe</div>
                            <div style="margin-top: 5px;">Thank you for visiting!</div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="text-center mt-4">
                        <a href="{% url 'patient_detail' token.patient.patient_id %}" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left"></i> Back to Patient
                        </a>
                        <form method="post" action="{% url 'tokens:reprint' token.id %}" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-primary" onclick="return confirm('Print this token again?')">
                                <i class="fas fa-print"></i> Print Token
                            </button>
                        </form>
                    </div>
                    
                    <!-- Token Information -->
                    <div class="mt-4">
                        <h6>Token Information:</h6>
                        <table class="table table-sm">
                            <tr>
                                <th width="30%">Token Number:</th>
                                <td>#{{ token.formatted_token_number }}</td>
                            </tr>
                            <tr>
                                <th>Generated At:</th>
                                <td>{{ token.generated_at|date:"M d, Y H:i" }}</td>
                            </tr>
                            <tr>
                                <th>Generated By:</th>
                                <td>{{ token.generated_by.get_full_name }}</td>
                            </tr>
                            <tr>
                                <th>Print Count:</th>
                                <td>{{ token.print_count }} time{{ token.print_count|pluralize }}</td>
                            </tr>
                            {% if token.printed_at %}
                            <tr>
                                <th>Last Printed:</th>
                                <td>{{ token.printed_at|date:"M d, Y H:i" }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .card-header, .btn, .table, h6 {
        display: none !important;
    }
    .token-preview {
        border: none !important;
        background: white !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
