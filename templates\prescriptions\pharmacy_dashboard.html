{% extends 'base.html' %}

{% block title %}Pharmacy Dashboard - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4">Pharmacy Dashboard</h1>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_pending }}</h4>
                            <p class="mb-0">Pending Prescriptions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-prescription fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ dispensed_today }}</h4>
                            <p class="mb-0">Dispensed Today</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ low_stock_count }}</h4>
                            <p class="mb-0">Low Stock Items</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-danger">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ out_of_stock_count }}</h4>
                            <p class="mb-0">Out of Stock</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Pending Prescriptions -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-prescription"></i> Pending Prescriptions</h5>
                    <a href="{% url 'prescription_list' %}?status=pending" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if pending_prescriptions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Prescription ID</th>
                                        <th>Patient</th>
                                        <th>Doctor</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prescription in pending_prescriptions %}
                                    <tr>
                                        <td><strong>{{ prescription.prescription_id }}</strong></td>
                                        <td>{{ prescription.patient.name }}</td>
                                        <td>Dr. {{ prescription.doctor.first_name }} {{ prescription.doctor.last_name }}</td>
                                        <td>
                                            {% if prescription.status == 'pending' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif prescription.status == 'partially_dispensed' %}
                                                <span class="badge bg-info">Partially Dispensed</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ prescription.created_at|date:"M d, H:i" }}</td>
                                        <td>
                                            <a href="{% url 'prescription_detail' prescription.prescription_id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="{% url 'dispense_prescription' prescription.prescription_id %}" class="btn btn-sm btn-success">
                                                <i class="fas fa-hand-holding-medical"></i> Dispense
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-prescription fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No pending prescriptions</h6>
                            <p class="text-muted">All prescriptions are up to date!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Stock Alerts -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle text-warning"></i> Stock Alerts</h5>
                </div>
                <div class="card-body">
                    {% if out_of_stock_medicines %}
                        <h6 class="text-danger">Out of Stock:</h6>
                        <ul class="list-unstyled mb-3">
                            {% for medicine in out_of_stock_medicines %}
                            <li class="mb-1">
                                <i class="fas fa-times-circle text-danger"></i> 
                                {{ medicine.name }} ({{ medicine.strength }})
                            </li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                    
                    {% if low_stock_medicines %}
                        <h6 class="text-warning">Low Stock:</h6>
                        <ul class="list-unstyled mb-3">
                            {% for medicine in low_stock_medicines %}
                            <li class="mb-1">
                                <i class="fas fa-exclamation-triangle text-warning"></i> 
                                {{ medicine.name }} ({{ medicine.strength }}) - {{ medicine.quantity_in_stock }} left
                            </li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                    
                    {% if not out_of_stock_medicines and not low_stock_medicines %}
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-success mb-0">All medicines are well stocked!</p>
                        </div>
                    {% endif %}
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> Contact Store Manager for inventory management
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="{% url 'prescription_list' %}" class="btn btn-primary btn-block mb-2">
                        <i class="fas fa-list"></i> All Prescriptions
                    </a>
                    <a href="{% url 'prescription_list' %}" class="btn btn-success btn-block mb-2">
                        <i class="fas fa-list"></i> All Prescriptions
                    </a>
                    <a href="{% url 'prescription_list' %}?status=pending" class="btn btn-info btn-block">
                        <i class="fas fa-clock"></i> Pending Prescriptions
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
