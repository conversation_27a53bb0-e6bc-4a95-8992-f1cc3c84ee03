{% extends 'base.html' %}

{% block title %}Stock Transactions - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-history"></i> Stock Transaction History</h1>
        <a href="{% url 'store:dashboard' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Store
        </a>
    </div>
    
    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="medicine" class="form-label">Medicine Name</label>
                    <input type="text" class="form-control" id="medicine" name="medicine" 
                           value="{{ medicine_filter }}" placeholder="Search by medicine name">
                </div>
                <div class="col-md-3">
                    <label for="type" class="form-label">Transaction Type</label>
                    <select class="form-control" id="type" name="type">
                        <option value="">All Types</option>
                        {% for value, label in transaction_types %}
                            <option value="{{ value }}" {% if transaction_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{% url 'store:transactions' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Transactions Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list"></i> Transaction Records</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Date & Time</th>
                                <th>Medicine</th>
                                <th>Transaction Type</th>
                                <th>Quantity</th>
                                <th>Reason</th>
                                <th>Reference</th>
                                <th>Created By</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ transaction.created_at|date:"M d, Y" }}</strong>
                                    <br>
                                    <small class="text-muted">{{ transaction.created_at|date:"H:i" }}</small>
                                </td>
                                <td>
                                    <strong>{{ transaction.medicine.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ transaction.medicine.strength }}</small>
                                </td>
                                <td>
                                    {% if transaction.transaction_type == 'in' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-arrow-up"></i> {{ transaction.get_transaction_type_display }}
                                        </span>
                                    {% elif transaction.transaction_type == 'out' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-arrow-down"></i> {{ transaction.get_transaction_type_display }}
                                        </span>
                                    {% elif transaction.transaction_type == 'adjustment' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-edit"></i> {{ transaction.get_transaction_type_display }}
                                        </span>
                                    {% elif transaction.transaction_type == 'expired' %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times"></i> {{ transaction.get_transaction_type_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if transaction.transaction_type == 'in' %}
                                        <span class="text-success">
                                            <strong>+{{ transaction.quantity }}</strong>
                                        </span>
                                    {% elif transaction.transaction_type == 'out' %}
                                        <span class="text-danger">
                                            <strong>{{ transaction.quantity }}</strong>
                                        </span>
                                    {% else %}
                                        <strong>{{ transaction.quantity }}</strong>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">{{ transaction.medicine.get_unit_display }}</small>
                                </td>
                                <td>{{ transaction.reason }}</td>
                                <td>
                                    {% if transaction.reference %}
                                        <span class="badge bg-info">{{ transaction.reference }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ transaction.created_by.first_name }} {{ transaction.created_by.last_name }}
                                    <br>
                                    <small class="text-muted">{{ transaction.created_by.get_role_display }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Transactions pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if medicine_filter %}&medicine={{ medicine_filter }}{% endif %}{% if transaction_filter %}&type={{ transaction_filter }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if medicine_filter %}&medicine={{ medicine_filter }}{% endif %}{% if transaction_filter %}&type={{ transaction_filter }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if medicine_filter %}&medicine={{ medicine_filter }}{% endif %}{% if transaction_filter %}&type={{ transaction_filter }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if medicine_filter %}&medicine={{ medicine_filter }}{% endif %}{% if transaction_filter %}&type={{ transaction_filter }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                <!-- Summary -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Transaction Summary</h6>
                            <p class="mb-0">
                                Showing {{ page_obj|length }} of {{ page_obj.paginator.count }} total transactions.
                                {% if medicine_filter or transaction_filter %}
                                    Filtered results based on your search criteria.
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No transactions found</h5>
                    {% if medicine_filter or transaction_filter %}
                        <p class="text-muted">No transactions match your search criteria.</p>
                        <a href="{% url 'store:transactions' %}" class="btn btn-primary">
                            <i class="fas fa-list"></i> View All Transactions
                        </a>
                    {% else %}
                        <p class="text-muted">No stock transactions have been recorded yet.</p>
                        <a href="{% url 'store:add_medicine' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Medicine to Store
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
