{% extends 'base.html' %}

{% block title %}Create Prescription - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Create Prescription</h1>
        <a href="{% url 'patient_detail' patient.patient_id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Patient
        </a>
    </div>
    
    <!-- Patient Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user"></i> Patient Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="30%">Patient ID:</th>
                            <td><strong>{{ patient.patient_id }}</strong></td>
                        </tr>
                        <tr>
                            <th>Name:</th>
                            <td>{{ patient.name }}</td>
                        </tr>
                        <tr>
                            <th>Age/Gender:</th>
                            <td>{{ patient.age }} years, {{ patient.get_gender_display }}</td>
                        </tr>
                        <tr>
                            <th>Contact:</th>
                            <td>{{ patient.contact }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-stethoscope"></i> Chief Complaint</h5>
                </div>
                <div class="card-body">
                    <p>{{ patient.symptoms|linebreaks }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Prescription Form -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-prescription"></i> Create Prescription</h5>
        </div>
        <div class="card-body">
            <form method="post" id="prescriptionForm">
                {% csrf_token %}
                
                <!-- Medical Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="diagnosis" class="form-label">Diagnosis *</label>
                            <textarea class="form-control" id="diagnosis" name="diagnosis" rows="3" required 
                                      placeholder="Enter the diagnosis..."></textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="symptoms" class="form-label">Current Symptoms</label>
                            <textarea class="form-control" id="symptoms" name="symptoms" rows="3" 
                                      placeholder="Additional symptoms observed..."></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="notes" class="form-label">Notes for Pharmacist</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2" 
                              placeholder="Special instructions or notes..."></textarea>
                </div>
                
                <!-- Medicines Section -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="fas fa-pills"></i> Prescribed Medicines</h5>
                        <button type="button" class="btn btn-success btn-sm" onclick="addMedicine()">
                            <i class="fas fa-plus"></i> Add Medicine
                        </button>
                    </div>
                    
                    <div id="medicinesContainer">
                        <!-- Medicine rows will be added here -->
                    </div>
                </div>
                
                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <a href="{% url 'patient_detail' patient.patient_id %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Prescription
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Medicine Row Template -->
<template id="medicineRowTemplate">
    <div class="medicine-row border rounded p-3 mb-3">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Medicine *</label>
                <select class="form-control medicine-select" name="medicine_id[]" required>
                    <option value="">Select Medicine</option>
                    {% for medicine in medicines %}
                        <option value="{{ medicine.id }}" data-stock="{{ medicine.quantity_in_stock }}" data-unit="{{ medicine.get_unit_display }}">
                            {{ medicine.name }} ({{ medicine.strength }}) - Stock: {{ medicine.quantity_in_stock }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Quantity *</label>
                <input type="number" class="form-control quantity-input" name="quantity[]" min="1" required>
                <small class="text-muted stock-info"></small>
            </div>
            <div class="col-md-3">
                <label class="form-label">Dosage *</label>
                <input type="text" class="form-control" name="dosage[]" required 
                       placeholder="e.g., 1 tablet twice daily">
            </div>
            <div class="col-md-2">
                <label class="form-label">Duration *</label>
                <input type="text" class="form-control" name="duration[]" required 
                       placeholder="e.g., 7 days">
            </div>
            <div class="col-md-2">
                <label class="form-label">Action</label>
                <button type="button" class="btn btn-danger btn-sm d-block" onclick="removeMedicine(this)">
                    <i class="fas fa-trash"></i> Remove
                </button>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-12">
                <label class="form-label">Special Instructions</label>
                <input type="text" class="form-control" name="instructions[]" 
                       placeholder="e.g., Take after meals, Avoid alcohol">
            </div>
        </div>
    </div>
</template>

<script>
let medicineCount = 0;

function addMedicine() {
    const template = document.getElementById('medicineRowTemplate');
    const container = document.getElementById('medicinesContainer');
    const clone = template.content.cloneNode(true);
    
    // Add event listener for medicine selection
    const select = clone.querySelector('.medicine-select');
    const quantityInput = clone.querySelector('.quantity-input');
    const stockInfo = clone.querySelector('.stock-info');
    
    select.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const stock = selectedOption.getAttribute('data-stock');
        const unit = selectedOption.getAttribute('data-unit');
        
        if (stock) {
            quantityInput.max = stock;
            stockInfo.textContent = `Available: ${stock} ${unit}`;
            stockInfo.className = stock > 0 ? 'text-muted stock-info' : 'text-danger stock-info';
        }
    });
    
    container.appendChild(clone);
    medicineCount++;
}

function removeMedicine(button) {
    button.closest('.medicine-row').remove();
}

// Add first medicine row on page load
document.addEventListener('DOMContentLoaded', function() {
    addMedicine();
});

// Form validation
document.getElementById('prescriptionForm').addEventListener('submit', function(e) {
    const medicineSelects = document.querySelectorAll('.medicine-select');
    const quantities = document.querySelectorAll('.quantity-input');
    
    if (medicineSelects.length === 0) {
        e.preventDefault();
        alert('Please add at least one medicine to the prescription.');
        return;
    }
    
    let hasValidMedicine = false;
    for (let i = 0; i < medicineSelects.length; i++) {
        if (medicineSelects[i].value && quantities[i].value) {
            hasValidMedicine = true;
            break;
        }
    }
    
    if (!hasValidMedicine) {
        e.preventDefault();
        alert('Please select at least one medicine with quantity.');
    }
});
</script>
{% endblock %}
