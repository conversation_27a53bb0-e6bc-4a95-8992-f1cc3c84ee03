{% extends 'base.html' %}

{% block title %}Register Patient - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-user-plus"></i> Register New Patient</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Patient Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="age" class="form-label">Age *</label>
                                    <input type="number" class="form-control" id="age" name="age" min="1" max="120" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="gender" class="form-label">Gender *</label>
                                    <select class="form-control" id="gender" name="gender" required>
                                        <option value="">Select Gender</option>
                                        <option value="M">Male</option>
                                        <option value="F">Female</option>
                                        <option value="O">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact" class="form-label">Contact Number *</label>
                                    <input type="tel" class="form-control" id="contact" name="contact" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="assigned_doctor" class="form-label">Assign Doctor</label>
                                    <select class="form-control" id="assigned_doctor" name="assigned_doctor">
                                        <option value="">Select Doctor (Optional)</option>
                                        {% for doctor in doctors %}
                                            <option value="{{ doctor.id }}">{{ doctor.first_name }} {{ doctor.last_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                        </div>
                        

                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'patient_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Register Patient
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Registration Guidelines</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Fill all required fields marked with *</li>
                        <li><i class="fas fa-check text-success"></i> Provide accurate contact information</li>

                        <li><i class="fas fa-check text-success"></i> Assign doctor if known</li>
                    </ul>
                    
                    <hr>
                    
                    <h6>Available Doctors:</h6>
                    <ul class="list-unstyled">
                        {% for doctor in doctors %}
                            <li><i class="fas fa-user-md text-primary"></i> {{ doctor.first_name }} {{ doctor.last_name }}</li>
                        {% empty %}
                            <li class="text-muted">No doctors available</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
