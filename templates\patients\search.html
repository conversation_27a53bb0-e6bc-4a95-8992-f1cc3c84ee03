{% extends 'base.html' %}

{% block title %}Search Patients - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4">Search Patients</h1>
    
    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get">
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" class="form-control" name="q" value="{{ query }}" 
                               placeholder="Search by patient name, ID, or contact number..." autofocus>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <a href="{% url 'patient_list' %}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> View All
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Search Results -->
    {% if query %}
        <div class="card">
            <div class="card-header">
                <h5>Search Results for "{{ query }}"</h5>
            </div>
            <div class="card-body">
                {% if patients %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Patient ID</th>
                                    <th>Name</th>
                                    <th>Age/Gender</th>
                                    <th>Contact</th>
                                    <th>Status</th>
                                    <th>Assigned Doctor</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for patient in patients %}
                                <tr>
                                    <td><strong>{{ patient.patient_id }}</strong></td>
                                    <td>{{ patient.name }}</td>
                                    <td>
                                        {{ patient.age }} years
                                        <br>
                                        <small class="text-muted">{{ patient.get_gender_display }}</small>
                                    </td>
                                    <td>{{ patient.contact }}</td>
                                    <td>
                                        {% if patient.status == 'registered' %}
                                            <span class="badge bg-warning">{{ patient.get_status_display }}</span>
                                        {% elif patient.status == 'consulting' %}
                                            <span class="badge bg-info">{{ patient.get_status_display }}</span>
                                        {% elif patient.status == 'prescribed' %}
                                            <span class="badge bg-primary">{{ patient.get_status_display }}</span>
                                        {% elif patient.status == 'completed' %}
                                            <span class="badge bg-success">{{ patient.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if patient.assigned_doctor %}
                                            {{ patient.assigned_doctor.first_name }} {{ patient.assigned_doctor.last_name }}
                                        {% else %}
                                            <span class="text-muted">Not assigned</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'patient_detail' patient.patient_id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        {% if user.is_doctor and patient.status != 'completed' %}
                                        <a href="{% url 'create_prescription' patient.patient_id %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-prescription"></i> Prescribe
                                        </a>
                                        {% endif %}
                                        {% if user.is_receptionist or user.is_admin %}
                                            {% if not patient.prescriptions.exists %}
                                                <a href="{% url 'delete_patient' patient.patient_id %}" class="btn btn-sm btn-outline-danger"
                                                   onclick="return confirm('Are you sure you want to delete {{ patient.name }}?')">
                                                    <i class="fas fa-trash"></i> Delete
                                                </a>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No patients found</h5>
                        <p class="text-muted">No patients match your search criteria "{{ query }}"</p>
                        <a href="{% url 'patient_list' %}" class="btn btn-primary">
                            <i class="fas fa-list"></i> View All Patients
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Enter search terms</h5>
                <p class="text-muted">Search for patients by name, patient ID, or contact number</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
