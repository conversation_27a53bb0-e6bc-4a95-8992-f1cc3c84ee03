{% extends 'base.html' %}

{% block title %}Delete Patient - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-danger">Delete Patient</h1>
        <a href="{% url 'patient_detail' patient.patient_id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Patient
        </a>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Confirm Patient Deletion</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-warning"></i> Warning!</h6>
                        <p class="mb-0">You are about to permanently delete this patient record. This action cannot be undone.</p>
                    </div>
                    
                    <!-- Patient Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Patient Details:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Patient ID:</th>
                                    <td><strong>{{ patient.patient_id }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Name:</th>
                                    <td>{{ patient.name }}</td>
                                </tr>
                                <tr>
                                    <th>Age:</th>
                                    <td>{{ patient.age }} years</td>
                                </tr>
                                <tr>
                                    <th>Gender:</th>
                                    <td>{{ patient.get_gender_display }}</td>
                                </tr>
                                <tr>
                                    <th>Contact:</th>
                                    <td>{{ patient.contact }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        {% if patient.status == 'registered' %}
                                            <span class="badge bg-warning">{{ patient.get_status_display }}</span>
                                        {% elif patient.status == 'consulting' %}
                                            <span class="badge bg-info">{{ patient.get_status_display }}</span>
                                        {% elif patient.status == 'prescribed' %}
                                            <span class="badge bg-primary">{{ patient.get_status_display }}</span>
                                        {% elif patient.status == 'completed' %}
                                            <span class="badge bg-success">{{ patient.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Registration Details:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Registered By:</th>
                                    <td>{{ patient.registered_by.first_name }} {{ patient.registered_by.last_name }}</td>
                                </tr>
                                <tr>
                                    <th>Registration Date:</th>
                                    <td>{{ patient.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <th>Assigned Doctor:</th>
                                    <td>
                                        {% if patient.assigned_doctor %}
                                            {{ patient.assigned_doctor.first_name }} {{ patient.assigned_doctor.last_name }}
                                        {% else %}
                                            <span class="text-muted">Not assigned</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Prescriptions:</th>
                                    <td>
                                        {% if patient.prescriptions.exists %}
                                            <span class="badge bg-warning">{{ patient.prescriptions.count }} prescription(s)</span>
                                        {% else %}
                                            <span class="text-muted">None</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Symptoms -->
                    <div class="mb-4">
                        <h6>Symptoms/Chief Complaint:</h6>
                        <div class="bg-light p-3 rounded">
                            {{ patient.symptoms|linebreaks }}
                        </div>
                    </div>
                    
                    <!-- Safety Checks -->
                    {% if patient.prescriptions.exists %}
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> Cannot Delete</h6>
                            <p class="mb-0">This patient has {{ patient.prescriptions.count }} existing prescription(s) and cannot be deleted. Please contact the administrator if you need to remove this patient record.</p>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'patient_detail' patient.patient_id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Patient
                            </a>
                            <a href="{% url 'patient_list' %}" class="btn btn-primary">
                                <i class="fas fa-list"></i> Back to Patient List
                            </a>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Safe to Delete</h6>
                            <p class="mb-0">This patient has no prescriptions and can be safely deleted.</p>
                        </div>
                        
                        <!-- Confirmation Form -->
                        <form method="post">
                            {% csrf_token %}
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'patient_detail' patient.patient_id %}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to delete this patient? This action cannot be undone.')">
                                    <i class="fas fa-trash"></i> Yes, Delete Patient
                                </button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
