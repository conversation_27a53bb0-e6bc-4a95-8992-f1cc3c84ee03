from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from patients.models import Patient

User = get_user_model()

class DailyTokenCounter(models.Model):
    """Model to track daily token counter that resets every day"""
    date = models.DateField(unique=True, default=timezone.now)
    current_token_number = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"Token Counter for {self.date}: {self.current_token_number}"

    @classmethod
    def get_next_token_number(cls):
        """Get the next token number for today"""
        today = timezone.now().date()
        counter, created = cls.objects.get_or_create(
            date=today,
            defaults={'current_token_number': 0}
        )
        counter.current_token_number += 1
        counter.save()
        return counter.current_token_number

    @classmethod
    def get_current_token_number(cls):
        """Get current token number for today without incrementing"""
        today = timezone.now().date()
        try:
            counter = cls.objects.get(date=today)
            return counter.current_token_number
        except cls.DoesNotExist:
            return 0


class PatientToken(models.Model):
    """Model to store patient token information"""
    patient = models.OneToOneField(Patient, on_delete=models.CASCADE, related_name='token')
    token_number = models.PositiveIntegerField()
    token_date = models.DateField(default=timezone.now)
    generated_at = models.DateTimeField(auto_now_add=True)
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='generated_tokens')
    printed_at = models.DateTimeField(null=True, blank=True)
    print_count = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['-generated_at']
        unique_together = ['token_number', 'token_date']

    def __str__(self):
        return f"Token #{self.token_number} - {self.patient.name} ({self.token_date})"

    @property
    def formatted_token_number(self):
        """Return formatted token number (e.g., 001, 002, etc.)"""
        return f"{self.token_number:03d}"

    def mark_as_printed(self):
        """Mark token as printed and increment print count"""
        self.printed_at = timezone.now()
        self.print_count += 1
        self.save()
