{% extends 'base.html' %}

{% block title %}Patients List - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Patients List</h1>
        <div>
            {% if user.is_receptionist or user.is_admin %}
            <button type="button" class="btn btn-danger me-2" id="bulkDeleteBtn" style="display: none;" onclick="bulkDeletePatients()">
                <i class="fas fa-trash"></i> Delete Selected
            </button>
            <a href="{% url 'register_patient' %}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Register New Patient
            </a>
            {% endif %}
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query }}" placeholder="Patient ID, Name, or Contact">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{% url 'patient_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Patients Table -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <form id="bulkDeleteForm" method="post" action="{% url 'bulk_delete_patients' %}">
                    {% csrf_token %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    {% if user.is_receptionist or user.is_admin %}
                                    <th width="50">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    {% endif %}
                                    <th>Patient ID</th>
                                    <th>Name</th>
                                    <th>Age/Gender</th>
                                    <th>Contact</th>
                                    <th>Status</th>
                                    <th>Assigned Doctor</th>
                                    <th>Registered Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                        <tbody>
                            {% for patient in page_obj %}
                            <tr>
                                {% if user.is_receptionist or user.is_admin %}
                                <td>
                                    {% if not patient.prescriptions.exists %}
                                        <input type="checkbox" name="patient_ids" value="{{ patient.patient_id }}"
                                               class="patient-checkbox" onchange="updateBulkDeleteButton()">
                                    {% else %}
                                        <i class="fas fa-lock text-muted" title="Cannot delete - has prescriptions"></i>
                                    {% endif %}
                                </td>
                                {% endif %}
                                <td><strong>{{ patient.patient_id }}</strong></td>
                                <td>{{ patient.name }}</td>
                                <td>
                                    {{ patient.age }} years
                                    <br>
                                    <small class="text-muted">{{ patient.get_gender_display }}</small>
                                </td>
                                <td>{{ patient.contact }}</td>
                                <td>
                                    {% if patient.status == 'registered' %}
                                        <span class="badge bg-warning">{{ patient.get_status_display }}</span>
                                    {% elif patient.status == 'consulting' %}
                                        <span class="badge bg-info">{{ patient.get_status_display }}</span>
                                    {% elif patient.status == 'prescribed' %}
                                        <span class="badge bg-primary">{{ patient.get_status_display }}</span>
                                    {% elif patient.status == 'completed' %}
                                        <span class="badge bg-success">{{ patient.get_status_display }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if patient.assigned_doctor %}
                                        {{ patient.assigned_doctor.first_name }} {{ patient.assigned_doctor.last_name }}
                                    {% else %}
                                        <span class="text-muted">Not assigned</span>
                                    {% endif %}
                                </td>
                                <td>{{ patient.created_at|date:"M d, Y H:i" }}</td>
                                <td>
                                    <a href="{% url 'patient_detail' patient.patient_id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    {% if user.is_receptionist or user.is_admin %}
                                        {% if not patient.prescriptions.exists %}
                                            <a href="{% url 'delete_patient' patient.patient_id %}" class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('Are you sure you want to delete {{ patient.name }}?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        {% else %}
                                            <span class="btn btn-sm btn-outline-secondary disabled" title="Cannot delete - has prescriptions">
                                                <i class="fas fa-lock"></i> Protected
                                            </span>
                                        {% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                </form>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Patients pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No patients found</h5>
                    {% if user.is_receptionist or user.is_admin %}
                    <p class="text-muted">Start by registering a new patient.</p>
                    <a href="{% url 'register_patient' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Register First Patient
                    </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.patient-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkDeleteButton();
}

function updateBulkDeleteButton() {
    const checkboxes = document.querySelectorAll('.patient-checkbox:checked');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

    if (checkboxes.length > 0) {
        bulkDeleteBtn.style.display = 'inline-block';
        bulkDeleteBtn.textContent = `Delete Selected (${checkboxes.length})`;
    } else {
        bulkDeleteBtn.style.display = 'none';
    }
}

function bulkDeletePatients() {
    const checkboxes = document.querySelectorAll('.patient-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('Please select at least one patient to delete.');
        return;
    }

    const count = checkboxes.length;
    const message = count === 1 ?
        'Are you sure you want to delete the selected patient?' :
        `Are you sure you want to delete ${count} selected patients?`;

    if (confirm(message + '\n\nThis action cannot be undone.')) {
        document.getElementById('bulkDeleteForm').submit();
    }
}

// Update button visibility on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBulkDeleteButton();
});
</script>
{% endblock %}
