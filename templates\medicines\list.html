{% extends 'base.html' %}

{% block title %}Medicine Inventory - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Medicine Inventory</h1>
        <a href="{% url 'add_medicine' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Medicine
        </a>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_medicines }}</h4>
                            <p class="mb-0">Total Medicines</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pills fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ low_stock_count }}</h4>
                            <p class="mb-0">Low Stock</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-danger">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ out_of_stock_count }}</h4>
                            <p class="mb-0">Out of Stock</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>₨{{ total_value|floatformat:0 }}</h4>
                            <p class="mb-0">Total Value</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query }}" placeholder="Medicine name, generic name, or manufacturer">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-control" id="category" name="category">
                        <option value="">All Categories</option>
                        {% for value, label in category_choices %}
                            <option value="{{ value }}" {% if category_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="stock" class="form-label">Stock Status</label>
                    <select class="form-control" id="stock" name="stock">
                        <option value="">All Stock</option>
                        <option value="low" {% if stock_filter == 'low' %}selected{% endif %}>Low Stock</option>
                        <option value="out" {% if stock_filter == 'out' %}selected{% endif %}>Out of Stock</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{% url 'medicine_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Medicines Table -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Medicine Name</th>
                                <th>Category</th>
                                <th>Strength</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Price/Unit</th>
                                <th>Total Value</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ medicine.name }}</strong>
                                    {% if medicine.generic_name %}
                                        <br><small class="text-muted">{{ medicine.generic_name }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ medicine.get_category_display }}</td>
                                <td>{{ medicine.strength }}</td>
                                <td>
                                    {{ medicine.quantity_in_stock }} {{ medicine.get_unit_display }}
                                    {% if medicine.minimum_stock_level %}
                                        <br><small class="text-muted">Min: {{ medicine.minimum_stock_level }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if medicine.quantity_in_stock == 0 %}
                                        <span class="badge bg-danger">Out of Stock</span>
                                    {% elif medicine.is_low_stock %}
                                        <span class="badge bg-warning">Low Stock</span>
                                    {% else %}
                                        <span class="badge bg-success">In Stock</span>
                                    {% endif %}
                                </td>
                                <td>₨{{ medicine.price_per_unit }}</td>
                                <td>₨{{ medicine.total_value|floatformat:2 }}</td>
                                <td>
                                    <a href="{% url 'update_stock' medicine.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Update Stock
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Medicines pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if stock_filter %}&stock={{ stock_filter }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if stock_filter %}&stock={{ stock_filter }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if stock_filter %}&stock={{ stock_filter }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if stock_filter %}&stock={{ stock_filter }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No medicines found</h5>
                    <p class="text-muted">Start by adding medicines to your inventory.</p>
                    <a href="{% url 'add_medicine' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Medicine
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
