from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from patients.models import Patient
from datetime import date

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a test patient for today to test dashboard statistics'

    def handle(self, *args, **options):
        try:
            receptionist = User.objects.get(role='receptionist')
            dr_kashif = User.objects.get(username='dr_kashif')
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('Required users not found. Please create users first.'))
            return

        # Create a test patient for today
        patient = Patient.objects.create(
            name='Test Patient Today',
            age=25,
            gender='M',
            contact='+92-***********',
            address='Test Address',
            symptoms='Test symptoms for today - fever and headache',
            registered_by=receptionist,
            assigned_doctor=dr_kashif
        )
        
        self.stdout.write(self.style.SUCCESS(f'Created test patient: {patient.name} ({patient.patient_id})'))
        self.stdout.write('This patient will show up in today\'s statistics on the dashboard.')
