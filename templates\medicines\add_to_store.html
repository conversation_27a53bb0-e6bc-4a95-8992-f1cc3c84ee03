{% extends 'base.html' %}

{% block title %}Add Medicine to Store - Tahir Hospital{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><i class="fas fa-plus"></i> Add Medicine to Store</h1>
        <a href="{% url 'store:inventory' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Inventory
        </a>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-store"></i> New Medicine Entry</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <h6 class="mb-3 text-primary">Medicine Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Medicine Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="generic_name" class="form-label">Generic Name</label>
                                    <input type="text" class="form-control" id="generic_name" name="generic_name">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category *</label>
                                    <select class="form-control" id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        {% for value, label in category_choices %}
                                            <option value="{{ value }}">{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="strength" class="form-label">Strength *</label>
                                    <input type="text" class="form-control" id="strength" name="strength" required 
                                           placeholder="e.g., 500mg, 10ml">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="manufacturer" class="form-label">Manufacturer</label>
                                    <input type="text" class="form-control" id="manufacturer" name="manufacturer">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Store Information -->
                        <hr>
                        <h6 class="mb-3 text-success">Store & Pricing Information</h6>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="quantity_in_stock" class="form-label">Initial Stock Quantity</label>
                                    <input type="number" class="form-control" id="quantity_in_stock" name="quantity_in_stock" 
                                           min="0" value="0">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="unit" class="form-label">Unit *</label>
                                    <select class="form-control" id="unit" name="unit" required>
                                        {% for value, label in unit_choices %}
                                            <option value="{{ value }}">{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="price_per_unit" class="form-label">Price per Unit (₨) *</label>
                                    <input type="number" class="form-control" id="price_per_unit" name="price_per_unit" 
                                           step="0.01" min="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="minimum_stock_level" class="form-label">Minimum Stock Level</label>
                                    <input type="number" class="form-control" id="minimum_stock_level" name="minimum_stock_level" 
                                           min="1" value="10">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expiry_date" class="form-label">Expiry Date</label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'store:inventory' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Add to Store
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-info-circle"></i> Store Guidelines</h5>
                </div>
                <div class="card-body">
                    <h6>Medicine Information:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Use standard medicine names</li>
                        <li><i class="fas fa-check text-success"></i> Include generic name if available</li>
                        <li><i class="fas fa-check text-success"></i> Specify strength clearly (mg, ml, etc.)</li>
                    </ul>
                    
                    <h6>Store Management:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Set appropriate minimum stock levels</li>
                        <li><i class="fas fa-check text-success"></i> Enter accurate pricing</li>
                        <li><i class="fas fa-check text-success"></i> Check expiry dates regularly</li>
                    </ul>
                    
                    <div class="alert alert-warning mt-3">
                        <small><i class="fas fa-exclamation-triangle"></i> 
                        Stock will be automatically deducted when medicines are dispensed by pharmacy.</small>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-list"></i> Common Categories</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <small>
                                • Tablets<br>
                                • Capsules<br>
                                • Syrups<br>
                                • Injections
                            </small>
                        </div>
                        <div class="col-6">
                            <small>
                                • Ointments<br>
                                • Drops<br>
                                • Inhalers<br>
                                • Others
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
