<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Tahir Hospital Management System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #2c3e50 !important;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            padding: 20px;
        }
        .card-header {
            background-color: #3498db;
            color: white;
        }
        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }
    </style>
</head>
<body>
    {% if user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-hospital"></i> Tahir Hospital
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    Welcome, {{ user.first_name|default:user.username }} ({{ user.get_role_display }})
                </span>
                <a class="nav-link" href="{% url 'logout' %}">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>
    {% endif %}

    <div class="container-fluid">
        <div class="row">
            {% if user.is_authenticated %}
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        {% if user.is_receptionist or user.is_admin %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'register_patient' %}">
                                <i class="fas fa-user-plus"></i> Register Patient
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'patient_list' %}">
                                <i class="fas fa-list"></i> All Patients
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'search_patients' %}">
                                <i class="fas fa-search"></i> Search Patients
                            </a>
                        </li>
                        {% endif %}
                        {% if user.is_doctor or user.is_admin %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'patient_list' %}">
                                <i class="fas fa-users"></i> My Patients
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'prescription_list' %}">
                                <i class="fas fa-prescription"></i> Prescriptions
                            </a>
                        </li>
                        {% endif %}
                        {% if user.is_store_manager or user.is_admin %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'store:dashboard' %}">
                                <i class="fas fa-store"></i> Store Management
                            </a>
                        </li>
                        {% endif %}

                        {% if user.is_pharmacy or user.is_admin %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'pharmacy_dashboard' %}">
                                <i class="fas fa-clipboard-list"></i> Pharmacy Dashboard
                            </a>
                        </li>
                        {% endif %}
                        {% if user.is_admin %}
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/">
                                <i class="fas fa-cog"></i> Admin Panel
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            <main class="col-md-10 ms-sm-auto main-content">
            {% else %}
            <main class="col-12">
            {% endif %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                {% block content %}
                {% endblock %}
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
